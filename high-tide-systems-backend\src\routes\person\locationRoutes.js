const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { LocationController, createLocationValidation } = require('../../controllers/locationController');
const AdvancedCacheMiddleware = require('../../middlewares/advancedCache');

// Rotas protegidas
router.use(authenticate);

router.post('/',
  createLocationValidation,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['locations:*'],
    tags: ['locations', 'reference']
  }),
  LocationController.create
);

router.get('/',
  AdvancedCacheMiddleware.referenceCache('locations:list', 1800), // 30 minutos
  LocationController.list
);

router.get('/:id',
  AdvancedCacheMiddleware.referenceCache('locations:detail', 1800), // 30 minutos
  LocationController.get
);

router.put('/:id',
  createLocationValidation,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['locations:*'],
    tags: ['locations', 'reference']
  }),
  LocationController.update
);

router.patch('/:id/status',
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['locations:*'],
    tags: ['locations', 'reference']
  }),
  LocationController.toggleStatus
);

router.delete('/:id',
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['locations:*'],
    tags: ['locations', 'reference']
  }),
  LocationController.delete
);

module.exports = router;