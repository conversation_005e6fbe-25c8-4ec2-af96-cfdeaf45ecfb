"use client";

import React, { useState, useEffect, useCallback } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import { format as dateFormat, parseISO, isAfter, isBefore, isEqual } from "date-fns";
import { ptBR } from "date-fns/locale";
import ModuleHeader from "@/components/ui/ModuleHeader";
import ExportMenu from "@/components/ui/ExportMenu";
import {
  Search,
  Calendar,
  Filter,
  Download,
  Trash2,
  Edit,
  Eye,
  RefreshCw,
  UserCheck,
  UserX,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import { AppointmentModal } from "@/components/calendar/AppointmentModal";
import AppointmentTable from "@/components/appointmentsReport/AppointmentTable";
import ReportFilters from "@/components/appointmentsReport/ReportFilter";
import ClientReportFilters from "@/components/appointmentsReport/ClientReportFilter";
import { usePermissions } from "@/hooks/usePermissions";
import { useToast } from "@/contexts/ToastContext";

// Tutorial steps para a página de relatório de agendamentos
const getReportTutorialSteps = (isClientUser) => [
  {
    title: isClientUser ? "Meus Agendamentos" : "Relatório",
    content: isClientUser
      ? "Esta tela permite visualizar seus agendamentos e de pessoas relacionadas a você."
      : "Esta tela permite visualizar e gerenciar todos os agendamentos do sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Filtros de Agendamentos",
    content: isClientUser
      ? "Use estes filtros para encontrar agendamentos específicos por data, status, paciente, local ou tipo de serviço."
      : "Use estes filtros para encontrar agendamentos específicos por data, status, profissional, paciente, local ou tipo de serviço.",
    selector: "form",
    position: "bottom"
  },
  {
    title: "Estatísticas",
    content: "Visualize estatísticas rápidas sobre os agendamentos filtrados, incluindo totais por status.",
    selector: ".grid.grid-cols-1.md\\:grid-cols-4",
    position: "top"
  },
  {
    title: "Tabela de Agendamentos",
    content: isClientUser
      ? "Veja seus agendamentos em formato de tabela."
      : "Veja todos os agendamentos em formato de tabela, com opções para editar, excluir e alterar o status.",
    selector: "table",
    position: "top"
  },
  {
    title: "Ações de Agendamento",
    content: isClientUser
      ? "Visualize detalhes dos seus agendamentos."
      : "Use estes botões para editar, excluir ou alterar o status de um agendamento.",
    selector: ".flex.space-x-2",
    position: "left"
  },
  {
    title: "Exportar Dados",
    content: "Exporte os agendamentos filtrados em diferentes formatos para análise externa.",
    selector: "button:has(span:contains('Exportar'))",
    position: "left"
  }
];

export const AppointmentsReport = () => {
  // Estado para armazenar os agendamentos
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAppointments, setTotalAppointments] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Estado para filtros
  const [filters, setFilters] = useState({
    search: "",
    startDate: null,
    endDate: null,
    status: [],
    providers: [],
    persons: [],
    locations: [],
    serviceTypes: [],
  });

  // Estado para controle do modal de agendamento
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);

  // Estado para confirmação de exclusão
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);
  const [appointmentToDelete, setAppointmentToDelete] = useState(null);

  // Permissões
  const { can, isClient } = usePermissions();
  const canEdit = can("scheduler.appointments.edit");
  const canDelete = can("scheduler.appointments.delete");

  // Get the appropriate tutorial steps based on user type
  const reportTutorialSteps = getReportTutorialSteps(isClient());

  // Toast notifications
  const { toast_success, toast_error } = useToast();

  // Estado para ordenação
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');

  // Função para carregar os agendamentos
  const loadAppointments = useCallback(async () => {
    setIsLoading(true);
    try {
      // Para clientes, não enviamos o filtro de providers (profissionais)
      const clientSafeFilters = { ...filters };

      // Se o usuário for cliente, remover o filtro de providers
      if (isClient() && clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {
        console.log("[CLIENT-FILTER] Removendo filtro de providers para cliente");
        delete clientSafeFilters.providers;
      }

      // Buscar todos os agendamentos de uma vez (sem paginação no backend)
      const response = await appointmentService.getAppointments({
        // Não enviamos page e limit para buscar todos os itens
        // ou enviamos um limite grande para garantir que todos os itens sejam retornados
        limit: 1000, // Um número grande para garantir que todos os itens sejam retornados
        search: clientSafeFilters.search || undefined,
        startDate: clientSafeFilters.startDate ? dateFormat(clientSafeFilters.startDate, "yyyy-MM-dd") : undefined,
        endDate: clientSafeFilters.endDate ? dateFormat(clientSafeFilters.endDate, "yyyy-MM-dd") : undefined,
        status: clientSafeFilters.status && clientSafeFilters.status.length > 0 ? clientSafeFilters.status : undefined,
        providers: !isClient() && clientSafeFilters.providers && clientSafeFilters.providers.length > 0 ? clientSafeFilters.providers : undefined,
        persons: clientSafeFilters.persons && clientSafeFilters.persons.length > 0 ? clientSafeFilters.persons : undefined,
        locations: clientSafeFilters.locations && clientSafeFilters.locations.length > 0 ? clientSafeFilters.locations : undefined,
        serviceTypes: clientSafeFilters.serviceTypes && clientSafeFilters.serviceTypes.length > 0 ? clientSafeFilters.serviceTypes : undefined,
      });

      // Armazenar todos os agendamentos
      const allAppointments = response.appointments || [];
      setAppointments(allAppointments);

      // Calcular o total de páginas com base no número de itens e no tamanho da página
      const total = allAppointments.length;
      const pages = Math.ceil(total / itemsPerPage);

      setTotalAppointments(total);
      setTotalPages(pages || 1);

      // Não precisamos aplicar ordenação e paginação manualmente
      // O ModuleTable já faz isso internamente
      setFilteredAppointments(allAppointments);
    } catch (error) {
      console.error("Erro ao carregar agendamentos:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar os agendamentos. Tente novamente."
      });
    } finally {
      setIsLoading(false);
    }
  }, [filters, toast_error, itemsPerPage]);

  // Carregar agendamentos quando os filtros ou a página mudarem
  useEffect(() => {
    loadAppointments();
  }, [loadAppointments]);

  // Função para aplicar filtros locais (sem chamar a API)
  const applyLocalFilters = useCallback(() => {
    if (!appointments.length) return;

    let filtered = [...appointments];

    // Aplicar filtro de busca
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        appointment =>
          appointment.title.toLowerCase().includes(searchLower) ||
          appointment.personfullName.toLowerCase().includes(searchLower) ||
          appointment.providerfullName.toLowerCase().includes(searchLower)
      );
    }

    // Aplicar filtro de data
    if (filters.startDate) {
      filtered = filtered.filter(appointment =>
        isAfter(new Date(appointment.startDate), new Date(filters.startDate)) ||
        isEqual(new Date(appointment.startDate), new Date(filters.startDate))
      );
    }

    if (filters.endDate) {
      filtered = filtered.filter(appointment =>
        isBefore(new Date(appointment.startDate), new Date(filters.endDate)) ||
        isEqual(new Date(appointment.startDate), new Date(filters.endDate))
      );
    }

    // Aplicar filtro de status
    if (filters.status.length > 0) {
      filtered = filtered.filter(appointment =>
        filters.status.includes(appointment.status)
      );
    }

    setFilteredAppointments(filtered);
  }, [appointments, filters]);

  // Aplicar filtros locais quando os filtros mudarem
  useEffect(() => {
    applyLocalFilters();
  }, [applyLocalFilters]);

  // Função para abrir o modal de edição
  const handleEditAppointment = (appointment) => {
    setSelectedAppointment(appointment);
    setIsModalOpen(true);
  };

  // Função para abrir o modal de confirmação de exclusão
  const handleDeleteAppointment = (appointment) => {
    setAppointmentToDelete(appointment);
    setShowConfirmDelete(true);
  };

  // Função para confirmar a exclusão
  const confirmDelete = async () => {
    if (!appointmentToDelete) return;

    setIsLoading(true);
    try {
      await appointmentService.deleteAppointment(appointmentToDelete.id);
      toast_success({
        title: "Sucesso",
        message: "Agendamento excluído com sucesso."
      });
      await loadAppointments();
    } catch (error) {
      console.error("Erro ao excluir agendamento:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível excluir o agendamento. Tente novamente."
      });
    } finally {
      setIsLoading(false);
      setShowConfirmDelete(false);
      setAppointmentToDelete(null);
    }
  };

  // Função para fechar o modal de exclusão
  const cancelDelete = () => {
    setShowConfirmDelete(false);
    setAppointmentToDelete(null);
  };

  // Função para alterar o status do agendamento
  const handleStatusChange = async (appointmentId, newStatus) => {
    setIsLoading(true);
    try {
      await appointmentService.updateAppointment(appointmentId, { status: newStatus });
      toast_success({
        title: "Sucesso",
        message: "Status do agendamento atualizado com sucesso."
      });
      await loadAppointments();
    } catch (error) {
      console.error("Erro ao atualizar status:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível atualizar o status do agendamento. Tente novamente."
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para exportar relatório
  const handleExport = async (format = "xlsx") => {
    setIsExporting(true);
    try {
      const success = await appointmentService.exportAppointments(filters, format);
      if (success) {
        toast_success({
          title: "Exportação concluída",
          message: `Os agendamentos foram exportados no formato ${format.toUpperCase()}.`
        });
      } else {
        toast_error({
          title: "Falha na exportação",
          message: "Não foi possível exportar os agendamentos."
        });
      }
    } catch (error) {
      console.error("Erro ao exportar:", error);
      toast_error({
        title: "Erro",
        message: "Ocorreu um erro ao exportar os agendamentos."
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Botões de ação para cada agendamento
  const renderActions = (appointment) => {
    const isCompleted = appointment.status === "COMPLETED";
    const isCancelled = appointment.status === "CANCELLED";

    return (
      <div className="flex space-x-2">
        <button
          className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors"
          onClick={() => handleEditAppointment(appointment)}
          disabled={!canEdit}
          title={canEdit ? "Editar agendamento" : "Sem permissão para editar"}
        >
          <Edit size={18} />
        </button>

        <button
          className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 transition-colors"
          onClick={() => handleDeleteAppointment(appointment)}
          disabled={!canDelete}
          title={canDelete ? "Excluir agendamento" : "Sem permissão para excluir"}
        >
          <Trash2 size={18} />
        </button>

        {!isCompleted && !isCancelled && (
          <button
            className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200 transition-colors"
            onClick={() => handleStatusChange(appointment.id, "COMPLETED")}
            disabled={!canEdit}
            title="Marcar como concluído"
          >
            <CheckCircle size={18} />
          </button>
        )}

        {!isCancelled && (
          <button
            className="p-1 text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-200 transition-colors"
            onClick={() => handleStatusChange(appointment.id, "CANCELLED")}
            disabled={!canEdit}
            title="Cancelar agendamento"
          >
            <XCircle size={18} />
          </button>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Título e botão de exportação */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          {isClient() ? (
            <Clock size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          ) : (
            <Calendar size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          )}
          {isClient() ? "Meus Agendamentos" : "Relatório de Agendamentos"}
        </h1>

        <ExportMenu
          onExport={handleExport}
          isExporting={isExporting}
          disabled={isLoading || filteredAppointments.length === 0}
          className="text-purple-600 dark:text-purple-400"
        />
      </div>

      {/* Cabeçalho da página com filtros integrados */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        description={isClient()
          ? "Visualize seus agendamentos e de pessoas relacionadas. Utilize os filtros abaixo para encontrar agendamentos específicos."
          : "Visualize, filtre e gerencie todos os agendamentos do sistema. Utilize os filtros abaixo para encontrar agendamentos específicos."}
        moduleColor="scheduler"
        helpButton={
          <TutorialTriggerButton
            steps={reportTutorialSteps}
            tutorialName="appointments-report-overview"
            size="md"
          >
            Ajuda
          </TutorialTriggerButton>
        }
        filters={
          isClient() ? (
            <ClientReportFilters
              filters={filters}
              setFilters={setFilters}
              onSearch={() => {
                setCurrentPage(1);  // Voltar para a primeira página ao pesquisar
                loadAppointments();
              }}
              onExport={handleExport}
              isLoading={isLoading}
            />
          ) : (
            <ReportFilters
              filters={filters}
              setFilters={setFilters}
              onSearch={() => {
                setCurrentPage(1);  // Voltar para a primeira página ao pesquisar
                loadAppointments();
              }}
              onExport={handleExport}
              isLoading={isLoading}
            />
          )
        }
      />

      {/* Estatísticas/Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Total de Agendamentos
          </h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {totalAppointments}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Agendamentos Concluídos
          </h3>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400">
            {filteredAppointments.filter(a => a.status === "COMPLETED").length}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Agendamentos Pendentes
          </h3>
          <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {filteredAppointments.filter(a => a.status === "PENDING").length}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Agendamentos Cancelados
          </h3>
          <p className="text-2xl font-bold text-red-600 dark:text-red-400">
            {filteredAppointments.filter(a => a.status === "CANCELLED").length}
          </p>
        </div>
      </div>

      {/* Tabela de agendamentos */}
      <AppointmentTable
        appointments={filteredAppointments}
        renderActions={renderActions}
        isLoading={isLoading}
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        setCurrentPage={setCurrentPage}
        setItemsPerPage={setItemsPerPage}
        onRefresh={loadAppointments}
      />

      {/* Modal de edição */}
      {isModalOpen && (
        <AppointmentModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedAppointment(null);
          }}
          selectedAppointment={selectedAppointment}
          onAppointmentChange={loadAppointments}
          canEdit={canEdit}
          canDelete={canDelete}
        />
      )}

      {/* Modal de confirmação de exclusão */}
      {showConfirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[11000]">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-bold mb-4 text-gray-900 dark:text-white">
              Confirmar Exclusão
            </h3>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              Tem certeza que deseja excluir o agendamento
              <span className="font-bold"> {appointmentToDelete?.title}</span>?
              Esta ação não pode ser desfeita.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Cancelar
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 text-white bg-red-600 rounded hover:bg-red-700"
              >
                Excluir
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default AppointmentsReport;