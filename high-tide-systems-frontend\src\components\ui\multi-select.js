'use client';

import React, { useState, useRef, useEffect } from 'react';
import { X, Check, ChevronDown, Loader2 } from 'lucide-react';

const MultiSelect = ({
  label,
  options = [],
  value = [],
  onChange = () => {},
  placeholder = "Selecionar...",
  disabled = false,
  loading = false,
  error = false,
  required = false,
  className = "",
  moduleOverride = null
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [mounted, setMounted] = useState(false);
  const containerRef = useRef(null);
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  // Montar o componente apenas no cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Verificar se o dropdown deve ser exibido acima ou abaixo do select
  useEffect(() => {
    if (!isOpen || !dropdownRef.current || !containerRef.current) return;

    const checkPosition = () => {
      const containerRect = containerRef.current.getBoundingClientRect();
      const dropdownHeight = dropdownRef.current.offsetHeight;
      const windowHeight = window.innerHeight;
      const spaceBelow = windowHeight - containerRect.bottom;

      // Se não houver espaço suficiente abaixo e houver mais espaço acima
      if (spaceBelow < dropdownHeight && containerRect.top > dropdownHeight) {
        dropdownRef.current.style.top = 'auto';
        dropdownRef.current.style.bottom = '100%';
        dropdownRef.current.style.marginTop = '0';
        dropdownRef.current.style.marginBottom = '4px';
      } else {
        dropdownRef.current.style.top = '100%';
        dropdownRef.current.style.bottom = 'auto';
        dropdownRef.current.style.marginTop = '4px';
        dropdownRef.current.style.marginBottom = '0';
      }
    };

    // Verificar a posição quando o dropdown é aberto
    checkPosition();

    // Verificar novamente após um pequeno atraso para garantir que o dropdown foi renderizado corretamente
    const timer = setTimeout(checkPosition, 50);

    return () => clearTimeout(timer);
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
        setSearch('');
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Log detalhado das opções recebidas
  useEffect(() => {
    if (label === "Profissionais") {
      console.log(`[MultiSelect-${label}] Opções recebidas:`, options);
      console.log(`[MultiSelect-${label}] Valores selecionados:`, value);
    }
  }, [options, value, label]);

  // Garantir que as opções sejam sempre um array
  const safeOptions = Array.isArray(options) ? options : [];

  // Filtrar opções com base na busca
  const filteredOptions = safeOptions.filter(option => {
    // Verificar se a opção é válida
    if (!option || typeof option !== 'object' || !option.label) {
      if (label === "Profissionais") {
        console.warn(`[MultiSelect-${label}] Opção inválida encontrada:`, option);
      }
      return false;
    }

    // Verificar se o label é uma string
    const optionLabel = String(option.label || '');
    const searchTerm = String(search || '').toLowerCase();

    return optionLabel.toLowerCase().includes(searchTerm);
  });

  const handleRemoveItem = (itemValue) => {
    if (!disabled && onChange) {
      onChange(value.filter(v => v !== itemValue));
    }
  };

  const handleSelectItem = (itemValue) => {
    if (disabled || !onChange) return;

    let newValue;
    if (value.includes(itemValue)) {
      newValue = value.filter(v => v !== itemValue);
    } else {
      newValue = [...value, itemValue];
    }

    console.log("MultiSelect - Valor alterado:", {
      anterior: value,
      novo: newValue,
      itemSelecionado: itemValue,
      tipoItem: typeof itemValue
    });

    onChange(newValue);
  };

  const handleToggleDropdown = (e) => {
    e.stopPropagation();
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setSearch('');
        inputRef.current?.focus();
      }
    }
  };

  const getContainerStyles = () => {
    const baseStyles = "relative w-full";
    const stateStyles = disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer";
    return `${baseStyles} ${stateStyles} ${className}`;
  };

  const getInputContainerStyles = () => {
    const baseStyles = "min-h-[42px] px-2 py-1.5 border rounded-lg transition-all duration-200 flex flex-wrap gap-1.5 items-center bg-white dark:bg-gray-700";

    // Background styles já incluídos no baseStyles

    // Focus styles based on module
    let focusStyles = '';
    if (isFocused) {
      if (moduleOverride === 'scheduler') {
        focusStyles = "ring-2 ring-module-scheduler-border dark:ring-module-scheduler-border-dark border-module-scheduler-border dark:border-module-scheduler-border-dark";
      } else if (moduleOverride === 'people') {
        focusStyles = "ring-2 ring-module-people-border dark:ring-module-people-border-dark border-module-people-border dark:border-module-people-border-dark";
      } else if (moduleOverride === 'admin') {
        focusStyles = "ring-2 ring-slate-300 dark:ring-slate-600 border-slate-400 dark:border-slate-500";
      } else {
        focusStyles = "ring-2 ring-primary-200 border-primary-300";
      }
    } else {
      if (moduleOverride === 'scheduler') {
        focusStyles = "hover:border-module-scheduler-border dark:hover:border-module-scheduler-border-dark";
      } else if (moduleOverride === 'people') {
        focusStyles = "hover:border-module-people-border dark:hover:border-module-people-border-dark";
      } else if (moduleOverride === 'admin') {
        focusStyles = "hover:border-slate-400 dark:hover:border-slate-500";
      } else {
        focusStyles = "hover:border-neutral-400";
      }
    }

    // Error styles based on module
    let errorStyles = '';
    if (error) {
      errorStyles = "border-error-500 hover:border-error-500";
    } else if (moduleOverride === 'scheduler') {
      errorStyles = "border-module-scheduler-border dark:border-module-scheduler-border-dark";
    } else if (moduleOverride === 'people') {
      errorStyles = "border-module-people-border dark:border-module-people-border-dark";
    } else {
      errorStyles = "border-neutral-300 dark:border-gray-600";
    }

    return `${baseStyles} ${focusStyles} ${errorStyles}`;
  };

  const getTagStyles = () => {
    if (moduleOverride === 'scheduler') {
      return "bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark text-module-scheduler-text dark:text-module-scheduler-text-dark text-sm px-2 py-1 rounded-md flex items-center gap-1.5 group transition-colors duration-200 hover:bg-module-scheduler-hover dark:hover:bg-module-scheduler-hover-dark";
    } else if (moduleOverride === 'people') {
      return "bg-module-people-bg dark:bg-module-people-bg-dark text-module-people-text dark:text-module-people-text-dark text-sm px-2 py-1 rounded-md flex items-center gap-1.5 group transition-colors duration-200 hover:bg-module-people-hover dark:hover:bg-module-people-hover-dark";
    } else if (moduleOverride === 'admin') {
      return "bg-slate-200 dark:bg-slate-600 text-slate-800 dark:text-slate-100 text-sm px-2 py-1 rounded-md flex items-center gap-1.5 group transition-colors duration-200 hover:bg-slate-300 dark:hover:bg-slate-500";
    } else {
      return "bg-primary-50 text-primary-700 text-sm px-2 py-1 rounded-md flex items-center gap-1.5 group transition-colors duration-200 hover:bg-primary-100";
    }
  };

  return (
    <div className={`${getContainerStyles()} overflow-visible`} ref={containerRef}>
      {label && (
        <label className={`flex gap-1 text-sm font-medium mb-1.5 ${
          moduleOverride === 'scheduler'
            ? 'text-module-scheduler-text dark:text-module-scheduler-text-dark'
            : moduleOverride === 'people'
              ? 'text-module-people-text dark:text-module-people-text-dark'
              : moduleOverride === 'admin'
                ? 'text-slate-700 dark:text-slate-300'
                : 'text-neutral-700 dark:text-gray-300'
        }`}>
          {label}
          {required && <span className="text-error-500">*</span>}
        </label>
      )}

      <div
        className={getInputContainerStyles()}
        onClick={handleToggleDropdown}
        onFocus={() => setIsFocused(true)}
        onBlur={() => !isOpen && setIsFocused(false)}
        tabIndex={0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-disabled={disabled}
      >
        {loading ? (
          <Loader2 className="w-4 h-4 text-neutral-400 animate-spin" />
        ) : value?.length > 0 ? (
          value.map(v => {
            const option = options?.find(opt => opt?.value === v);
            return option ? (
              <span key={v} className={getTagStyles()}>
                {option.label}
                <X
                  size={14}
                  className={`${
                    moduleOverride === 'admin'
                      ? 'text-slate-700 dark:text-slate-200'
                      : 'text-primary-600'
                  } opacity-60 group-hover:opacity-100 cursor-pointer`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveItem(v);
                  }}
                />
              </span>
            ) : null;
          })
        ) : (
          <span className={`py-0.5 ${
            moduleOverride === 'scheduler'
              ? 'text-module-scheduler-text/50 dark:text-module-scheduler-text-dark/50'
              : moduleOverride === 'people'
                ? 'text-module-people-text/50 dark:text-module-people-text-dark/50'
                : moduleOverride === 'admin'
                  ? 'text-neutral-500 dark:text-gray-400'
                  : 'text-neutral-400'
          }`}>{placeholder}</span>
        )}

        <ChevronDown
          size={18}
          className={`ml-auto transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''} ${
            moduleOverride === 'scheduler'
              ? 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark'
              : moduleOverride === 'people'
                ? 'text-module-people-icon dark:text-module-people-icon-dark'
                : moduleOverride === 'admin'
                  ? 'text-slate-500 dark:text-slate-400'
                  : 'text-neutral-400'
          }`}
        />
      </div>

      {error && (
        <p className="mt-1 text-sm text-error-500">
          {typeof error === 'string' ? error : 'Este campo é obrigatório'}
        </p>
      )}

      {/* Dropdown renderizado diretamente no DOM */}
      {isOpen && mounted && (
        <div
          ref={dropdownRef}
          className={`absolute z-[1000] w-full rounded-lg shadow-lg animate-in fade-in-0 zoom-in-95 ${
            moduleOverride === 'scheduler'
              ? 'bg-white dark:bg-gray-800 border border-module-scheduler-border dark:border-module-scheduler-border-dark'
              : moduleOverride === 'people'
                ? 'bg-white dark:bg-gray-800 border border-module-people-border dark:border-module-people-border-dark'
                : moduleOverride === 'admin'
                  ? 'bg-white dark:bg-gray-800 border border-neutral-200 dark:border-gray-600'
                  : 'bg-white border border-neutral-200'
          }`}
          style={{
            top: '100%',
            left: 0
          }}
        >
          <div className={`p-2 ${
            moduleOverride === 'scheduler'
              ? 'border-b border-module-scheduler-border/30 dark:border-module-scheduler-border-dark/30'
              : moduleOverride === 'people'
                ? 'border-b border-module-people-border/30 dark:border-module-people-border-dark/30'
                : moduleOverride === 'admin'
                  ? 'border-b border-neutral-100 dark:border-gray-700'
                  : 'border-b border-neutral-100'
          }`}>
            <div className="relative">
              <input
                ref={inputRef}
                type="text"
                className={`w-full pl-2 pr-8 py-1.5 text-sm rounded-md focus:outline-none ${
                  moduleOverride === 'scheduler'
                    ? 'border border-module-scheduler-border dark:border-module-scheduler-border-dark bg-neutral-50 dark:bg-gray-700 placeholder-module-scheduler-text/50 dark:placeholder-module-scheduler-text-dark/50 text-module-scheduler-text dark:text-module-scheduler-text-dark focus:ring-2 focus:ring-module-scheduler-border dark:focus:ring-module-scheduler-border-dark focus:border-module-scheduler-border dark:focus:border-module-scheduler-border-dark'
                    : moduleOverride === 'people'
                      ? 'border border-module-people-border dark:border-module-people-border-dark bg-neutral-50 dark:bg-gray-700 placeholder-module-people-text/50 dark:placeholder-module-people-text-dark/50 text-module-people-text dark:text-module-people-text-dark focus:ring-2 focus:ring-module-people-border dark:focus:ring-module-people-border-dark focus:border-module-people-border dark:focus:border-module-people-border-dark'
                      : moduleOverride === 'admin'
                        ? 'border border-neutral-200 dark:border-gray-600 bg-neutral-50 dark:bg-gray-700 placeholder-neutral-400 dark:placeholder-gray-400 text-neutral-800 dark:text-gray-200 focus:ring-2 focus:ring-slate-300 dark:focus:ring-slate-600 focus:border-slate-400 dark:focus:border-slate-500'
                        : 'border border-neutral-200 bg-neutral-50 placeholder-neutral-400 focus:ring-2 focus:ring-primary-200 focus:border-primary-300'
                }`}
                placeholder="Buscar..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>

          <div className="max-h-60 overflow-auto">
            {filteredOptions.length === 0 ? (
              <div className={`p-3 text-sm text-center ${
                moduleOverride === 'scheduler'
                  ? 'text-module-scheduler-text/70 dark:text-module-scheduler-text-dark/70'
                  : moduleOverride === 'people'
                    ? 'text-module-people-text/70 dark:text-module-people-text-dark/70'
                    : moduleOverride === 'admin'
                      ? 'text-neutral-500 dark:text-gray-400'
                      : 'text-neutral-500'
              }`}>
                {label === "Profissionais" && (
                  <div>
                    <p>Nenhum profissional encontrado</p>
                    <button
                      className={`mt-2 px-3 py-1 rounded-md ${
                        moduleOverride === 'scheduler'
                          ? 'bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark text-module-scheduler-text dark:text-module-scheduler-text-dark hover:bg-module-scheduler-hover dark:hover:bg-module-scheduler-hover-dark'
                          : moduleOverride === 'people'
                            ? 'bg-module-people-bg dark:bg-module-people-bg-dark text-module-people-text dark:text-module-people-text-dark hover:bg-module-people-hover dark:hover:bg-module-people-hover-dark'
                            : moduleOverride === 'admin'
                              ? 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-200 hover:bg-slate-200 dark:hover:bg-slate-600'
                              : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log("[MultiSelect] Forçando recarregamento de dados...");
                        // Adicionar alguns itens de teste
                        const testOptions = [
                          { value: "test-provider-1", label: "Dr. Teste 1" },
                          { value: "test-provider-2", label: "Dr. Teste 2" },
                          { value: "test-provider-3", label: "Dr. Teste 3" }
                        ];
                        // Atualizar as opções diretamente no componente pai
                        if (typeof onChange === 'function') {
                          onChange([]);
                        }
                      }}
                    >
                      Recarregar
                    </button>
                  </div>
                ) || "Nenhum resultado encontrado"}
              </div>
            ) : (
              <div className="py-1">
                {filteredOptions.map((option, index) => (
                  <div
                    key={option.value || `option-${index}`}
                    className={`
                      flex items-center justify-between px-3 py-2 text-sm cursor-pointer
                      transition-colors duration-150
                      ${value.includes(option.value)
                        ? moduleOverride === 'scheduler'
                          ? 'bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark text-module-scheduler-text dark:text-module-scheduler-text-dark'
                          : moduleOverride === 'people'
                            ? 'bg-module-people-bg dark:bg-module-people-bg-dark text-module-people-text dark:text-module-people-text-dark'
                            : moduleOverride === 'admin'
                              ? 'bg-slate-200 dark:bg-slate-600 text-slate-800 dark:text-slate-100'
                              : 'bg-primary-50 text-primary-700'
                        : moduleOverride === 'scheduler'
                          ? 'text-module-scheduler-text dark:text-module-scheduler-text-dark hover:bg-module-scheduler-hover dark:hover:bg-module-scheduler-hover-dark'
                          : moduleOverride === 'people'
                            ? 'text-module-people-text dark:text-module-people-text-dark hover:bg-module-people-hover dark:hover:bg-module-people-hover-dark'
                            : moduleOverride === 'admin'
                              ? 'text-slate-800 dark:text-slate-100 hover:bg-slate-100 dark:hover:bg-slate-700'
                              : 'text-neutral-700 hover:bg-neutral-50'}
                    `}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectItem(option.value);
                    }}
                    role="option"
                    aria-selected={value.includes(option.value)}
                  >
                    <span>{option.label}</span>
                    {value.includes(option.value) && (
                      <Check size={16} className={
                        moduleOverride === 'scheduler'
                          ? 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark'
                          : moduleOverride === 'people'
                            ? 'text-module-people-icon dark:text-module-people-icon-dark'
                            : moduleOverride === 'admin'
                              ? 'text-slate-700 dark:text-slate-200'
                              : 'text-primary-500'
                      } />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiSelect;