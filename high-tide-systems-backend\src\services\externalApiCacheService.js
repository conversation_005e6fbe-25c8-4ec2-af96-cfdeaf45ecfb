// src/services/externalApiCacheService.js
const cacheService = require('./cacheService');
const loggerService = require('./loggerService');
const axios = require('axios');

/**
 * Serviço de cache para APIs externas
 * Implementa cache inteligente para reduzir chamadas externas
 */
class ExternalApiCacheService {
  constructor() {
    this.defaultTTL = {
      cep: 7 * 24 * 60 * 60, // 7 dias
      stripe: 60 * 60, // 1 hora
      external: 30 * 60 // 30 minutos padrão
    };
  }

  /**
   * Buscar CEP com cache
   * @param {string} cep - CEP para buscar
   * @returns {Promise<Object>} - Dados do CEP
   */
  async getCepData(cep) {
    const cleanCep = cep.replace(/\D/g, '');
    
    if (cleanCep.length !== 8) {
      throw new Error('CEP deve ter 8 dígitos');
    }

    const cacheKey = `cep:${cleanCep}`;
    
    return await cacheService.getOrSet(
      cacheKey,
      async () => {
        loggerService.info('CEP_API_CALL', { cep: cleanCep });
        
        try {
          // Tentar ViaCEP primeiro
          const response = await axios.get(`https://viacep.com.br/ws/${cleanCep}/json/`, {
            timeout: 5000
          });

          if (response.data.erro) {
            throw new Error('CEP não encontrado');
          }

          const cepData = {
            cep: response.data.cep,
            logradouro: response.data.logradouro,
            complemento: response.data.complemento,
            bairro: response.data.bairro,
            localidade: response.data.localidade,
            uf: response.data.uf,
            ibge: response.data.ibge,
            gia: response.data.gia,
            ddd: response.data.ddd,
            siafi: response.data.siafi,
            source: 'viacep',
            cachedAt: new Date().toISOString()
          };

          loggerService.info('CEP_API_SUCCESS', { 
            cep: cleanCep, 
            city: cepData.localidade,
            state: cepData.uf 
          });

          return cepData;
        } catch (error) {
          loggerService.error('CEP_API_ERROR', {
            cep: cleanCep,
            error: error.message
          });
          throw error;
        }
      },
      this.defaultTTL.cep,
      { logHit: true, logMiss: true }
    );
  }

  /**
   * Validar dados do Stripe com cache
   * @param {string} customerId - ID do cliente no Stripe
   * @returns {Promise<Object>} - Dados do cliente
   */
  async getStripeCustomer(customerId) {
    const cacheKey = `stripe:customer:${customerId}`;
    
    return await cacheService.getOrSet(
      cacheKey,
      async () => {
        loggerService.info('STRIPE_API_CALL', { customerId });
        
        try {
          const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
          const customer = await stripe.customers.retrieve(customerId);

          const customerData = {
            id: customer.id,
            email: customer.email,
            name: customer.name,
            created: customer.created,
            currency: customer.currency,
            default_source: customer.default_source,
            subscriptions: customer.subscriptions?.data || [],
            source: 'stripe',
            cachedAt: new Date().toISOString()
          };

          loggerService.info('STRIPE_API_SUCCESS', { 
            customerId,
            email: customerData.email 
          });

          return customerData;
        } catch (error) {
          loggerService.error('STRIPE_API_ERROR', {
            customerId,
            error: error.message
          });
          throw error;
        }
      },
      this.defaultTTL.stripe,
      { logHit: true, logMiss: true }
    );
  }

  /**
   * Cache genérico para APIs externas
   * @param {string} apiName - Nome da API
   * @param {string} endpoint - Endpoint da API
   * @param {Function} apiCall - Função que faz a chamada da API
   * @param {Object} options - Opções de cache
   * @returns {Promise<any>} - Resultado da API
   */
  async cacheExternalApi(apiName, endpoint, apiCall, options = {}) {
    const {
      ttl = this.defaultTTL.external,
      keyParams = {},
      forceRefresh = false
    } = options;

    const cacheKey = cacheService.generateKey(`external:${apiName}:${endpoint}`, keyParams);
    
    if (forceRefresh) {
      await cacheService.delete(cacheKey);
    }

    return await cacheService.getOrSet(
      cacheKey,
      async () => {
        loggerService.info('EXTERNAL_API_CALL', { 
          apiName, 
          endpoint,
          keyParams 
        });
        
        try {
          const result = await apiCall();
          
          loggerService.info('EXTERNAL_API_SUCCESS', { 
            apiName, 
            endpoint 
          });

          return {
            data: result,
            source: apiName,
            cachedAt: new Date().toISOString()
          };
        } catch (error) {
          loggerService.error('EXTERNAL_API_ERROR', {
            apiName,
            endpoint,
            error: error.message
          });
          throw error;
        }
      },
      ttl,
      { logHit: true, logMiss: true }
    );
  }

  /**
   * Invalidar cache de API específica
   * @param {string} apiName - Nome da API
   * @param {string} pattern - Padrão para invalidar (opcional)
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async invalidateApiCache(apiName, pattern = '*') {
    try {
      await cacheService.clear(`external:${apiName}:${pattern}`);
      
      loggerService.info('EXTERNAL_API_CACHE_INVALIDATED', { 
        apiName, 
        pattern 
      });

      return true;
    } catch (error) {
      loggerService.error('EXTERNAL_API_CACHE_INVALIDATION_ERROR', {
        apiName,
        pattern,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Pré-carregar cache para dados frequentemente acessados
   * @param {Array} cepList - Lista de CEPs para pré-carregar
   * @returns {Promise<Object>} - Resultado do pré-carregamento
   */
  async preloadCepCache(cepList) {
    const results = {
      success: 0,
      errors: 0,
      details: []
    };

    loggerService.info('CEP_PRELOAD_START', { count: cepList.length });

    for (const cep of cepList) {
      try {
        await this.getCepData(cep);
        results.success++;
        results.details.push({ cep, status: 'success' });
      } catch (error) {
        results.errors++;
        results.details.push({ 
          cep, 
          status: 'error', 
          error: error.message 
        });
      }
    }

    loggerService.info('CEP_PRELOAD_COMPLETE', results);
    return results;
  }

  /**
   * Estatísticas de uso de APIs externas
   * @returns {Promise<Object>} - Estatísticas
   */
  async getApiStats() {
    try {
      const stats = await cacheService.getStats();
      
      // Contar chaves por tipo de API
      const apiKeys = {
        cep: 0,
        stripe: 0,
        external: 0
      };

      // Esta é uma implementação simplificada
      // Em produção, você poderia usar SCAN para contar chaves específicas
      
      return {
        cache: stats,
        apis: apiKeys,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      loggerService.error('API_STATS_ERROR', {
        error: error.message
      });
      return null;
    }
  }
}

// Exportar instância única
module.exports = new ExternalApiCacheService();
