// Estilos para o dark mode
export const getDarkModeStyles = () => `
  .fc-theme-standard {
    background-color: #1f2937; /* gray-800 */
  }
  .fc-theme-standard .fc-toolbar-title {
    color: #f3f4f6; /* gray-100 */
  }
  .fc-theme-standard .fc-button {
    background-color: #7e22ce; /* purple-700 */
    color: #f3f4f6; /* gray-100 */
    border-color: #7e22ce; /* purple-700 */
  }
  .fc-theme-standard .fc-button:hover {
    background-color: #6b21a8; /* purple-800 */
    border-color: #6b21a8; /* purple-800 */
  }
  .fc-theme-standard .fc-button-active {
    background-color: #8b5cf6; /* violet-500 */
    color: #f3f4f6; /* gray-100 */
    border-color: #7c3aed; /* violet-600 */
  }
  .fc-theme-standard .fc-button-active:hover {
    background-color: #7c3aed; /* violet-600 */
    border-color: #6d28d9; /* violet-700 */
  }
  .fc-theme-standard .fc-col-header {
    background-color: #374151; /* gray-700 */
  }
  .fc-theme-standard .fc-col-header-cell-cushion {
    color: #f3f4f6; /* gray-100 */
  }
  .fc-theme-standard .fc-daygrid-day-number {
    color: #f3f4f6; /* gray-100 */
  }
  .fc-theme-standard .fc-day {
    border-color: #4b5563; /* gray-600 */
  }
  .fc-theme-standard .fc-day-other {
    background-color: #1f2937; /* gray-800 */
  }
  .fc-theme-standard .fc-timegrid-slot {
    border-color: #4b5563; /* gray-600 */
  }
  .fc-theme-standard .fc-timegrid-slot-label {
    color: #f3f4f6; /* gray-100 */
  }
  .fc-theme-standard .fc-timegrid-slots td {
    border-color: #4b5563; /* gray-600 */
  }
  .fc-theme-standard .fc-event {
    border-color: transparent;
  }
  .fc-theme-standard .fc-event-title {
    color: #f3f4f6; /* gray-100 */
  }
  .fc-theme-standard .fc-event-time {
    color: #f3f4f6; /* gray-100 */
  }
  .fc-theme-standard .fc-more-link {
    color: #60a5fa; /* blue-400 */
  }
  .fc-theme-standard .fc-day-today {
    background-color: rgba(147, 51, 234, 0.1); /* purple-700 with opacity */
  }
  .fc-theme-standard th, .fc-theme-standard td {
    border-color: #4b5563; /* gray-600 */
  }

  /* Mudar a cor da linha do horário atual (nowIndicator) no modo escuro */
  .fc-theme-standard .fc-timegrid-now-indicator-line {
    border-color: #f97316 !important; /* Tailwind orange-500 */
  }

  .fc-theme-standard .fc-timegrid-now-indicator-arrow {
    border-color: #f97316 !important; /* Tailwind orange-500 */
    border-top-color: transparent !important;
    border-bottom-color: transparent !important;
  }
`;

// Estilos para eventos sobrepostos com divisão horizontal
export const getEventWidthStyles = () => `
  /* Configurações para permitir eventos sobrepostos */
  .fc-timegrid-event-harness {
    /* Permitir que o FullCalendar posicione os eventos */
    width: auto !important;
  }

  /* Estilos básicos para eventos */
  .fc-timegrid-event {
    border-radius: 6px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin: 1px;
    overflow: hidden;
    min-height: 20px !important; /* Altura mínima para eventos muito curtos */
  }

  /* Garantir que o conteúdo dos eventos não seja comprimido */
  .fc-timegrid-event-harness {
    min-height: 20px !important;
  }

  /* Limitar a largura da coluna de eventos, deixando espaço à direita */
  .fc-timegrid-col-events {
    width: 90% !important; /* Limitar a largura para 90% da coluna */
    max-width: 90% !important; /* Garantir que não ultrapasse 90% */
    right: auto !important;
  }

  /* Ajustar o espaçamento interno para eventos pequenos */
  .fc-timegrid-event.fc-event-mirror,
  .fc-timegrid-event.fc-event-selected {
    padding: 0 !important;
  }

  /* Efeito de hover para eventos */
  .fc-timegrid-event:hover {
    z-index: 10 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  /* Melhorar a aparência dos eventos */
  .fc-event-main {
    padding: 2px;
  }

  /* Garantir que o texto dentro dos eventos seja legível */
  .fc-timegrid-event .text-xs {
    font-size: 0.7rem;
    line-height: 1.1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Ajustar o espaçamento para eventos compactos */
  .fc-timegrid-event.compact .text-xs {
    font-size: 0.65rem;
    line-height: 1;
  }

  /* Garantir que eventos muito estreitos ainda mostrem informações essenciais */
  @media (max-width: 768px) {
    .fc-timegrid-event .text-xs {
      font-size: 0.65rem;
    }
  }

  /* Classe personalizada para eventos na visualização de semana/dia */
  .calendar-timegrid-event {
    margin: 0 1px;
  }

  /* Garantir que eventos sobrepostos sejam exibidos lado a lado */
  .fc-timegrid-col-events {
    margin: 0 !important;
    position: relative !important;
  }

  /* Remover qualquer transformação que possa afetar o posicionamento */
  .fc-timegrid-col-events {
    transform: none !important;
  }

  /* Garantir que o FullCalendar possa posicionar os eventos corretamente */
  .fc-timegrid-event-harness {
    margin: 0 !important;
    left: 0 !important;
  }

  /* Garantir que não haja espaço entre eventos no mesmo horário */
  .fc-timegrid-event-harness + .fc-timegrid-event-harness {
    margin-left: 0 !important;
    left: 0 !important;
  }

  /* Estilos para o indicador de múltiplos eventos */
  [class^="multiple-events-indicator-"] {
    position: absolute; /* Posicionamento absoluto dentro do evento */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000; /* Usar valor do design system para dropdowns */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 700;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.2s ease;
    animation: fadeIn 0.3s ease-in-out;
  }

  /* Animação de fade in */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Efeito hover para o indicador */
  [class^="multiple-events-indicator-"]:hover {
    filter: brightness(1.1);
  }

  /* Estilo para o ícone dentro do indicador */
  [class^="multiple-events-indicator-"] svg {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }





  @keyframes pulseBorder {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.8);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(255, 87, 34, 0);
      transform: scale(1.05);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 87, 34, 0);
      transform: scale(1);
    }
  }

  /* Garantir que o slot de tempo tenha posição relativa para posicionar o indicador */
  .fc-timegrid-slot-lane {
    position: relative !important;
  }

  /* Mudar a cor da linha do horário atual (nowIndicator) de vermelho para laranja */
  .fc-timegrid-now-indicator-line {
    border-color: #f97316 !important; /* Tailwind orange-500 */
  }

  .fc-timegrid-now-indicator-arrow {
    border-color: #f97316 !important; /* Tailwind orange-500 */
    border-top-color: transparent !important;
    border-bottom-color: transparent !important;
  }

  /* Estilo específico para badges na visualização mensal */
  .month-view-badge {
    position: absolute !important;
    bottom: 2px !important;
    right: 2px !important;
    top: auto !important;
    width: 18px !important;
    height: 18px !important;
    background-color: #9333ea !important; /* Roxo mais escuro (violet-600) */
    color: white !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    font-weight: bold !important;
    line-height: 1 !important;
    z-index: 1000 !important;
    cursor: pointer !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid white !important;
    pointer-events: auto !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Estilo para o link "mais" do FullCalendar */
  .fc-daygrid-more-link {
    position: absolute !important;
    bottom: 2px !important;
    right: 2px !important;
    width: 18px !important;
    height: 18px !important;
    background-color: #9333ea !important;
    color: white !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    font-weight: bold !important;
    line-height: 1 !important;
    z-index: 1000 !important;
    cursor: pointer !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid white !important;
    text-decoration: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
`;