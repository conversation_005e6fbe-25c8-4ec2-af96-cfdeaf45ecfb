// src/middlewares/advancedCache.js
const cacheService = require('../services/cacheService');
const loggerService = require('../services/loggerService');

/**
 * Cache inteligente com estratégias avançadas
 */
class AdvancedCacheMiddleware {
  /**
   * Cache com estratégia baseada no tipo de dados
   * @param {Object} options - Opções de configuração
   * @returns {Function} - Middleware Express
   */
  static smartCache(options = {}) {
    const {
      prefix,
      ttl = 300, // 5 minutos padrão
      strategy = 'standard', // standard, reference, session, search
      tags = [],
      compress = false,
      skipIf = null, // função para pular cache
      keyGenerator = null // função customizada para gerar chave
    } = options;

    return async (req, res, next) => {
      // Não usar cache para métodos que modificam dados
      if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
        return next();
      }

      // Verificar se deve pular o cache
      if (skipIf && skipIf(req)) {
        return next();
      }

      const requestLogger = req.logger || loggerService.child({
        middleware: 'advancedCache',
        strategy,
        prefix
      });

      try {
        // Gerar chave de cache
        const cacheKey = keyGenerator 
          ? keyGenerator(req, prefix)
          : this.generateCacheKey(req, prefix, strategy);

        requestLogger.debug('CACHE_KEY_GENERATED', { cacheKey, strategy });

        // Verificar se a resposta está em cache
        const cachedResponse = compress 
          ? await cacheService.getCompressed(cacheKey)
          : await cacheService.get(cacheKey);

        if (cachedResponse) {
          requestLogger.cache('GET', cacheKey, true, 0);
          requestLogger.info('CACHE_HIT', { cacheKey, strategy });
          
          // Adicionar headers de cache
          res.set({
            'X-Cache': 'HIT',
            'X-Cache-Key': cacheKey,
            'X-Cache-Strategy': strategy
          });

          return res.json(cachedResponse);
        }

        // Cache miss - interceptar resposta
        const originalJson = res.json;
        res.json = function(data) {
          // Restaurar método original
          res.json = originalJson;

          // Armazenar no cache apenas se for resposta bem-sucedida
          if (res.statusCode >= 200 && res.statusCode < 300) {
            const cachePromise = compress
              ? cacheService.setCompressed(cacheKey, data, ttl, true)
              : cacheService.set(cacheKey, data, ttl);

            cachePromise
              .then(() => {
                requestLogger.cache('SET', cacheKey, false, 0);
                requestLogger.info('CACHE_SET', { cacheKey, strategy, ttl });
              })
              .catch(err => {
                requestLogger.error('CACHE_SET_ERROR', {
                  error: err.message,
                  cacheKey
                });
              });
          }

          // Adicionar headers de cache
          res.set({
            'X-Cache': 'MISS',
            'X-Cache-Key': cacheKey,
            'X-Cache-Strategy': strategy
          });

          return originalJson.call(this, data);
        };

        requestLogger.cache('GET', cacheKey, false, 0);
        requestLogger.info('CACHE_MISS', { cacheKey, strategy });
        next();

      } catch (error) {
        requestLogger.error('CACHE_MIDDLEWARE_ERROR', {
          error: error.message,
          stack: error.stack
        });
        next();
      }
    };
  }

  /**
   * Cache para dados de referência (longa duração)
   */
  static referenceCache(prefix, ttl = 3600) {
    return this.smartCache({
      prefix,
      ttl,
      strategy: 'reference',
      tags: ['reference'],
      compress: false
    });
  }

  /**
   * Cache para dados de sessão do usuário
   */
  static sessionCache(prefix, ttl = 1800) {
    return this.smartCache({
      prefix,
      ttl,
      strategy: 'session',
      tags: ['session'],
      keyGenerator: (req, prefix) => {
        return `${prefix}:user:${req.user?.id}:company:${req.user?.companyId}`;
      }
    });
  }

  /**
   * Cache para resultados de busca
   */
  static searchCache(prefix, ttl = 120) {
    return this.smartCache({
      prefix,
      ttl,
      strategy: 'search',
      tags: ['search'],
      skipIf: (req) => {
        // Pular cache se não houver parâmetros de busca
        const hasSearchParams = req.query.search || req.query.filter || req.query.sort;
        return !hasSearchParams;
      }
    });
  }

  /**
   * Cache para dados grandes com compressão
   */
  static largeDataCache(prefix, ttl = 900) {
    return this.smartCache({
      prefix,
      ttl,
      strategy: 'large',
      tags: ['large'],
      compress: true
    });
  }

  /**
   * Middleware para invalidação inteligente de cache
   */
  static smartInvalidation(options = {}) {
    const {
      patterns = [],
      tags = [],
      conditional = null // função para invalidação condicional
    } = options;

    return async (req, res, next) => {
      const requestLogger = req.logger || loggerService.child({
        middleware: 'smartInvalidation'
      });

      // Interceptar resposta para invalidar cache após sucesso
      const originalJson = res.json;
      const originalSend = res.send;

      const invalidateCache = async () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            // Verificar condição se fornecida
            if (conditional && !conditional(req, res)) {
              return;
            }

            // Invalidar por padrões
            for (const pattern of patterns) {
              await cacheService.clear(pattern);
              requestLogger.info('CACHE_INVALIDATED_PATTERN', { pattern });
            }

            // Invalidar por tags
            if (tags.length > 0) {
              await cacheService.invalidateByTags(tags);
              requestLogger.info('CACHE_INVALIDATED_TAGS', { tags });
            }

          } catch (error) {
            requestLogger.error('CACHE_INVALIDATION_ERROR', {
              error: error.message,
              patterns,
              tags
            });
          }
        }
      };

      res.json = function(data) {
        res.json = originalJson;
        invalidateCache();
        return originalJson.call(this, data);
      };

      res.send = function(data) {
        res.send = originalSend;
        invalidateCache();
        return originalSend.call(this, data);
      };

      next();
    };
  }

  /**
   * Gerar chave de cache baseada na estratégia
   */
  static generateCacheKey(req, prefix, strategy) {
    const baseParams = {
      userId: req.user?.id,
      companyId: req.user?.companyId,
      role: req.user?.role
    };

    switch (strategy) {
      case 'reference':
        // Para dados de referência, não incluir usuário específico
        return cacheService.generateKey(`${prefix}:${req.originalUrl}`, {
          companyId: req.user?.companyId
        });

      case 'session':
        // Para dados de sessão, incluir usuário específico
        return cacheService.generateKey(`${prefix}:session`, baseParams);

      case 'search':
        // Para busca, incluir parâmetros de query
        return cacheService.generateKey(`${prefix}:search:${req.originalUrl}`, {
          ...baseParams,
          ...req.query
        });

      case 'large':
        // Para dados grandes, usar hash dos parâmetros
        const crypto = require('crypto');
        const paramsHash = crypto
          .createHash('md5')
          .update(JSON.stringify({ ...baseParams, ...req.query }))
          .digest('hex');
        return `${prefix}:large:${paramsHash}`;

      default:
        // Estratégia padrão
        return cacheService.generateKey(`${prefix}:${req.originalUrl}`, baseParams);
    }
  }

  /**
   * Middleware para estatísticas de cache
   */
  static cacheStats() {
    return async (req, res, next) => {
      if (req.path === '/cache/stats' && req.method === 'GET') {
        try {
          const stats = await cacheService.getStats();
          return res.json({
            cache: stats,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          return res.status(500).json({
            error: 'Erro ao obter estatísticas do cache',
            message: error.message
          });
        }
      }
      next();
    };
  }
}

module.exports = AdvancedCacheMiddleware;
