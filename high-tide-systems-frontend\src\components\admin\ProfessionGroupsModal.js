import React, { useState, useEffect } from "react";
import { X, Search, Edit, Trash, Tag, CheckCircle, XCircle, Briefcase, Building, Plus } from "lucide-react";
import { professionsService } from "@/app/modules/admin/services/professionsService";
import { useToast } from "@/contexts/ToastContext";
import { useAuth } from "@/contexts/AuthContext";

const ProfessionGroupsModal = ({ isOpen, onClose, onEditGroup, onDeleteGroup, onSuccess }) => {
  const { toast_error } = useToast();
  const { user } = useAuth();
  const isSystemAdmin = user?.role === "SYSTEM_ADMIN";

  const [groups, setGroups] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [filteredGroups, setFilteredGroups] = useState([]);

  useEffect(() => {
    if (isOpen) {
      loadGroups();
    }
  }, [isOpen]);

  useEffect(() => {
    if (search.trim() === "") {
      setFilteredGroups(groups);
    } else {
      const searchLower = search.toLowerCase();
      setFilteredGroups(
        groups.filter(
          (group) =>
            group.name.toLowerCase().includes(searchLower) ||
            (group.description && group.description.toLowerCase().includes(searchLower))
        )
      );
    }
  }, [search, groups]);

  const loadGroups = async () => {
    setIsLoading(true);
    try {
      const data = await professionsService.getProfessionGroups();
      setGroups(data);
      setFilteredGroups(data);
    } catch (error) {
      console.error("Erro ao carregar grupos de profissões:", error);
      toast_error("Erro ao carregar grupos de profissões");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const handleEditGroup = (group) => {
    if (onEditGroup) {
      onEditGroup(group);
      onClose();
    }
  };

  const handleDeleteGroup = (group) => {
    if (onDeleteGroup) {
      onDeleteGroup(group);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>
      <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-4 border-b border-neutral-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-neutral-800 dark:text-neutral-100">
            Gerenciar Grupos de Profissões
          </h2>
          <div className="flex items-center gap-2">
            <button
              onClick={() => {
                if (onEditGroup) {
                  onEditGroup(null); // Passar null para indicar criação de novo grupo
                  onClose();
                }
              }}
              className="flex items-center gap-1 px-3 py-1.5 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors text-sm"
            >
              <Plus size={16} />
              <span>Novo Grupo</span>
            </button>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X size={20} className="text-neutral-500 dark:text-neutral-400" />
            </button>
          </div>
        </div>

        <div className="p-4">
          {/* Barra de pesquisa */}
          <div className="mb-4 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
            <input
              type="text"
              placeholder="Buscar grupos..."
              value={search}
              onChange={handleSearch}
              className="w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
            />
          </div>

          {/* Lista de grupos */}
          <div className="overflow-hidden rounded-lg border border-neutral-200 dark:border-gray-700">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
                <thead>
                  <tr className="bg-neutral-50 dark:bg-gray-700">
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                      Grupo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                      Descrição
                    </th>
                    {isSystemAdmin && (
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                        Empresa
                      </th>
                    )}
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                      Profissões
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-neutral-200 dark:divide-gray-700">
                  {isLoading ? (
                    <tr>
                      <td
                        colSpan={isSystemAdmin ? "6" : "5"}
                        className="px-6 py-4 text-center text-neutral-500 dark:text-neutral-400"
                      >
                        <div className="flex justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary-500 dark:border-primary-400"></div>
                        </div>
                      </td>
                    </tr>
                  ) : filteredGroups.length === 0 ? (
                    <tr>
                      <td
                        colSpan={isSystemAdmin ? "6" : "5"}
                        className="px-6 py-4 text-center text-neutral-500 dark:text-neutral-400"
                      >
                        Nenhum grupo encontrado
                      </td>
                    </tr>
                  ) : (
                    filteredGroups.map((group) => (
                      <tr key={group.id} className="hover:bg-neutral-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center">
                              <Tag size={20} />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                                {group.name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2">
                            {group.description || (
                              <span className="text-neutral-400 dark:text-neutral-500 italic">
                                Sem descrição
                              </span>
                            )}
                          </div>
                        </td>
                        {isSystemAdmin && (
                          <td className="px-6 py-4 whitespace-nowrap">
                            {group.company ? (
                              <div className="flex items-center gap-1">
                                <Building size={14} className="text-neutral-500" />
                                <span className="text-sm text-neutral-600 dark:text-neutral-300">
                                  {group.company.name}
                                </span>
                              </div>
                            ) : (
                              <span className="text-neutral-400 dark:text-neutral-500 italic">
                                Sem empresa
                              </span>
                            )}
                          </td>
                        )}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Briefcase size={16} className="text-neutral-400 dark:text-neutral-500 mr-2" />
                            <span className="text-sm text-neutral-600 dark:text-neutral-300">
                              {group._count?.professions || 0} profissões
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${
                              group.active
                                ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                                : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                            }`}
                          >
                            {group.active ? (
                              <>
                                <CheckCircle size={12} />
                                <span>Ativo</span>
                              </>
                            ) : (
                              <>
                                <XCircle size={12} />
                                <span>Inativo</span>
                              </>
                            )}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end gap-2">
                            <button
                              onClick={() => handleEditGroup(group)}
                              className="p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-colors"
                              title="Editar grupo"
                            >
                              <Edit size={18} />
                            </button>
                            <button
                              onClick={() => handleDeleteGroup(group)}
                              className="p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
                              title="Excluir grupo"
                              disabled={group._count?.professions > 0}
                            >
                              <Trash size={18} className={group._count?.professions > 0 ? "opacity-50 cursor-not-allowed" : ""} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div className="flex justify-end p-4 border-t border-neutral-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors"
          >
            Fechar
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfessionGroupsModal;
