const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function restoreData(backupFilePath) {
  try {
    console.log('🔄 Iniciando restauração dos dados...\n');

    // Verificar se o arquivo existe
    if (!fs.existsSync(backupFilePath)) {
      throw new Error(`Arquivo de backup não encontrado: ${backupFilePath}`);
    }

    // Carregar dados do backup
    const backupData = JSON.parse(fs.readFileSync(backupFilePath, 'utf8'));
    console.log(`📁 Carregando backup de: ${backupData.timestamp}`);

    // 1. Restaurar grupos de profissão primeiro (dependência)
    console.log('\n👨‍💼 Restaurando grupos de profissão...');
    for (const group of backupData.data.professionGroups) {
      const { professions, ...groupData } = group;
      
      // Criar grupo
      const createdGroup = await prisma.professionGroup.create({
        data: {
          ...groupData,
          createdAt: new Date(groupData.createdAt),
          updatedAt: new Date(groupData.updatedAt)
        }
      });

      // Criar profissões do grupo
      for (const profession of professions) {
        await prisma.profession.create({
          data: {
            ...profession,
            groupId: createdGroup.id,
            createdAt: new Date(profession.createdAt),
            updatedAt: new Date(profession.updatedAt)
          }
        });
      }
    }
    console.log(`   ✅ ${backupData.data.professionGroups.length} grupos restaurados`);

    // 2. Restaurar empresas
    console.log('\n🏢 Restaurando empresas...');
    for (const company of backupData.data.companies) {
      const { users, subscription, branches, ...companyData } = company;
      
      // Criar empresa
      const createdCompany = await prisma.company.create({
        data: {
          ...companyData,
          createdAt: new Date(companyData.createdAt),
          updatedAt: new Date(companyData.updatedAt)
        }
      });

      // Restaurar branches
      const branchMap = new Map();
      for (const branch of branches) {
        const createdBranch = await prisma.branch.create({
          data: {
            ...branch,
            companyId: createdCompany.id,
            createdAt: new Date(branch.createdAt),
            updatedAt: new Date(branch.updatedAt)
          }
        });
        branchMap.set(branch.id, createdBranch.id);
      }

      // Restaurar usuários
      for (const user of users) {
        // Hash da senha (assumindo que era 'Teste@123' ou similar)
        const hashedPassword = await bcrypt.hash('Teste@123', 10);
        
        await prisma.user.create({
          data: {
            ...user,
            password: hashedPassword,
            companyId: createdCompany.id,
            branchId: user.branchId ? branchMap.get(user.branchId) : null,
            birthDate: user.birthDate ? new Date(user.birthDate) : null,
            createdAt: new Date(user.createdAt),
            updatedAt: new Date(user.updatedAt),
            WorkingHours: {
              create: [
                { dayOfWeek: 1, startTimeMinutes: 480, endTimeMinutes: 1080, isActive: true },
                { dayOfWeek: 2, startTimeMinutes: 480, endTimeMinutes: 1080, isActive: true },
                { dayOfWeek: 3, startTimeMinutes: 480, endTimeMinutes: 1080, isActive: true },
                { dayOfWeek: 4, startTimeMinutes: 480, endTimeMinutes: 1080, isActive: true },
                { dayOfWeek: 5, startTimeMinutes: 480, endTimeMinutes: 1080, isActive: true },
                { dayOfWeek: 6, startTimeMinutes: 480, endTimeMinutes: 720, isActive: false },
                { dayOfWeek: 0, startTimeMinutes: 480, endTimeMinutes: 720, isActive: false }
              ]
            }
          }
        });
      }

      // Restaurar subscription
      if (subscription) {
        const { modules, invoices, ...subscriptionData } = subscription;
        
        const createdSubscription = await prisma.subscription.create({
          data: {
            ...subscriptionData,
            companyId: createdCompany.id,
            startDate: new Date(subscriptionData.startDate),
            endDate: subscriptionData.endDate ? new Date(subscriptionData.endDate) : null,
            trialEndDate: subscriptionData.trialEndDate ? new Date(subscriptionData.trialEndDate) : null,
            lastBillingDate: subscriptionData.lastBillingDate ? new Date(subscriptionData.lastBillingDate) : null,
            nextBillingDate: subscriptionData.nextBillingDate ? new Date(subscriptionData.nextBillingDate) : null,
            stripeCurrentPeriodEnd: subscriptionData.stripeCurrentPeriodEnd ? new Date(subscriptionData.stripeCurrentPeriodEnd) : null,
            createdAt: new Date(subscriptionData.createdAt),
            updatedAt: new Date(subscriptionData.updatedAt)
          }
        });

        // Restaurar módulos da subscription
        for (const module of modules) {
          await prisma.subscriptionModule.create({
            data: {
              ...module,
              subscriptionId: createdSubscription.id,
              addedAt: new Date(module.addedAt)
            }
          });
        }

        // Restaurar invoices
        for (const invoice of invoices) {
          await prisma.invoice.create({
            data: {
              ...invoice,
              companyId: createdCompany.id,
              dueDate: new Date(invoice.dueDate),
              paidAt: invoice.paidAt ? new Date(invoice.paidAt) : null,
              createdAt: new Date(invoice.createdAt),
              updatedAt: new Date(invoice.updatedAt)
            }
          });
        }
      }
    }
    console.log(`   ✅ ${backupData.data.companies.length} empresas restauradas`);

    // 3. Restaurar pacientes
    console.log('\n🏥 Restaurando pacientes...');
    for (const patient of backupData.data.patients) {
      const { appointments, company, ...patientData } = patient;
      
      await prisma.patient.create({
        data: {
          ...patientData,
          birthDate: patientData.birthDate ? new Date(patientData.birthDate) : null,
          createdAt: new Date(patientData.createdAt),
          updatedAt: new Date(patientData.updatedAt)
        }
      });
    }
    console.log(`   ✅ ${backupData.data.patients.length} pacientes restaurados`);

    // 4. Restaurar serviços
    console.log('\n🛠️ Restaurando serviços...');
    for (const service of backupData.data.services) {
      const { company, ...serviceData } = service;
      
      await prisma.service.create({
        data: {
          ...serviceData,
          createdAt: new Date(serviceData.createdAt),
          updatedAt: new Date(serviceData.updatedAt)
        }
      });
    }
    console.log(`   ✅ ${backupData.data.services.length} serviços restaurados`);

    console.log('\n✅ Restauração concluída com sucesso!');
    console.log('⚠️  Nota: Todas as senhas foram redefinidas para "Teste@123"');

  } catch (error) {
    console.error('❌ Erro ao restaurar dados:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const backupFile = process.argv[2];
  if (!backupFile) {
    console.error('❌ Uso: node restore-data.js <caminho-do-backup>');
    process.exit(1);
  }
  restoreData(backupFile);
}

module.exports = { restoreData };
