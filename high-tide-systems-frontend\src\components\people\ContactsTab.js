"use client";

import React, { useState, useEffect } from "react";
import {
  UserPlus,
  Users,
  Loader2,
  Edit,
  Trash,
  Mail,
  Phone,
  FileText,
  X,
  User
} from "lucide-react";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { contactsService } from "@/app/modules/people/services/contactsService";
import MaskedInput from "@/components/common/MaskedInput";
import { ModuleSelect, ModuleFormGroup } from "@/components/ui";

const ContactsTab = ({ personId, onClose, isCreating = false, onAddTempContact, tempContacts = [] }) => {
  const [contacts, setContacts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const [contactFormOpen, setContactFormOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState(null);
  const [notesModalOpen, setNotesModalOpen] = useState(false);
  const [selectedNotes, setSelectedNotes] = useState({ name: "", notes: "" });

  // Contact form state
  const [formData, setFormData] = useState({
    name: "",
    relationship: "",
    email: "",
    phone: "",
    notes: ""
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (personId) {
      loadContacts();
    }
  }, [personId]);

  const loadContacts = async () => {
    if (!personId) return;

    setIsLoading(true);
    setError(null);

    try {
      const data = await contactsService.getContactsByPerson(personId);
      setContacts(data || []);
    } catch (err) {
      console.error("Error fetching contacts:", err);
      setError("Não foi possível carregar os contatos.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenContactForm = (contact = null) => {
    if (contact) {
      setFormData({
        name: contact.name || "",
        relationship: contact.relationship || "",
        email: contact.email || "",
        phone: contact.phone || "",
        notes: contact.notes || ""
      });
      setSelectedContact(contact);
    } else {
      setFormData({
        name: "",
        relationship: "",
        email: "",
        phone: "",
        notes: ""
      });
      setSelectedContact(null);
    }

    setFormErrors({});
    setContactFormOpen(true);
  };

  const handleCloseContactForm = () => {
    setContactFormOpen(false);
    setSelectedContact(null);
    setFormData({
      name: "",
      relationship: "",
      email: "",
      phone: "",
      notes: ""
    });
    setFormErrors({});
  };

  const handleDeleteContact = (contact) => {
    setContactToDelete(contact.id);
    setConfirmDialogOpen(true);
  };

  const handleViewNotes = (contact) => {
    setSelectedNotes({
      name: contact.name,
      notes: contact.notes || ""
    });
    setNotesModalOpen(true);
  };

  const confirmDeleteContact = async () => {
    if (!contactToDelete) return;

    try {
      await contactsService.deleteContact(contactToDelete);
      setContacts(contacts.filter(c => c.id !== contactToDelete));
      setConfirmDialogOpen(false);
      setContactToDelete(null);
    } catch (err) {
      console.error("Error deleting contact:", err);
      setError("Não foi possível excluir o contato.");
    }
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is modified
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateContactForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = "Nome é obrigatório";
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Email inválido";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmitContact = async (e) => {
    e.preventDefault();

    if (!validateContactForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        personId: personId,
        name: formData.name,
        relationship: formData.relationship || null,
        email: formData.email || null,
        phone: formData.phone ? formData.phone.replace(/\D/g, "") : null,
        notes: formData.notes || null
      };

      if (isCreating) {
        // Modo de criação - adicionar contato temporário
        if (onAddTempContact) {
          onAddTempContact({
            id: `temp-${Date.now()}`,
            ...payload
          });
        }
        handleCloseContactForm();
      } else {
        // Modo de edição - salvar no banco de dados
        if (selectedContact) {
          // Update existing contact
          await contactsService.updateContact(selectedContact.id, payload);
        } else {
          // Create new contact
          await contactsService.createContact(payload);
        }

        // Reload contacts and close form
        loadContacts();
        handleCloseContactForm();
      }
    } catch (err) {
      console.error("Error saving contact:", err);
      setFormErrors({
        submit: err.response?.data?.message || "Erro ao salvar contato"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatPhone = (phone) => {
    if (!phone) return "N/A";

    // Phone format: (00) 00000-0000
    const phoneNumbers = phone.replace(/\D/g, '');
    return phoneNumbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  };

  // Se não estiver no modo de criação e não tiver personId, mostrar mensagem
  if (!isCreating && !personId) {
    return (
      <div className="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4">
        <div className="flex items-center gap-2">
          <Users size={24} />
          <h3 className="text-lg font-semibold">Contatos</h3>
        </div>
        <p className="text-center">Salve os dados básicos da pessoa antes de adicionar contatos.</p>
        <button
          onClick={() => onClose()}
          className="mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
        >
          Voltar para Informações
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-white">Contatos</h3>
          {isCreating ? (
            <span className="text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full">
              {tempContacts.length}
            </span>
          ) : isLoading ? (
            <Loader2 size={16} className="animate-spin text-neutral-400 dark:text-gray-500" />
          ) : (
            <span className="text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full">
              {contacts.length}
            </span>
          )}
        </div>

        <button
          onClick={() => handleOpenContactForm()}
          className="flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
        >
          <UserPlus size={16} />
          <span>Adicionar Contato</span>
        </button>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400">
          {error}
          <button
            onClick={loadContacts}
            className="ml-2 underline hover:no-underline"
          >
            Tentar novamente
          </button>
        </div>
      )}

      {/* Contacts list */}
      {isCreating ? (
        // Modo de criação - mostrar contatos temporários
        tempContacts.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center">
            <Users size={48} className="text-neutral-300 dark:text-gray-600 mb-4" />
            <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2">Nenhum contato</h4>
            <p className="text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center">
              Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de referência.
            </p>
            <button
              onClick={() => handleOpenContactForm()}
              className="flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
            >
              <UserPlus size={16} />
              <span>Adicionar Contato</span>
            </button>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20">
            <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
              <thead>
                <tr className="bg-neutral-50 dark:bg-gray-900">
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                    Nome
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                    Relacionamento
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                    Telefone
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700">
                {tempContacts.map(contact => (
                  <tr key={contact.id} className="hover:bg-neutral-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium">
                          {contact.name.charAt(0).toUpperCase()}
                        </div>
                        <span className="ml-2 text-neutral-700 dark:text-gray-200">{contact.name}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                        {contact.relationship || "Não especificado"}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      {contact.email ? (
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3 text-neutral-400 dark:text-gray-500" />
                          <span className="text-primary-600 dark:text-primary-400">
                            {contact.email}
                          </span>
                        </div>
                      ) : (
                        <span className="text-neutral-400 dark:text-gray-500">Não informado</span>
                      )}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      {contact.phone ? (
                        <div className="flex items-center gap-1">
                          <Phone className="h-3 w-3 text-neutral-400 dark:text-gray-500" />
                          <span className="text-neutral-700 dark:text-gray-300">
                            {formatPhone(contact.phone)}
                          </span>
                        </div>
                      ) : (
                        <span className="text-neutral-400 dark:text-gray-500">Não informado</span>
                      )}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-right">
                      <div className="flex justify-end">
                        <button
                          onClick={() => handleOpenContactForm(contact)}
                          className="p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                          title="Editar"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteContact(contact)}
                          className="p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                          title="Excluir"
                        >
                          <Trash size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )
      ) : isLoading ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center">
          <div className="flex flex-col items-center">
            <Loader2 size={32} className="text-primary-500 dark:text-primary-400 animate-spin mb-4" />
            <p className="text-neutral-600 dark:text-gray-300">Carregando contatos...</p>
          </div>
        </div>
      ) : contacts.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center">
          <Users size={48} className="text-neutral-300 dark:text-gray-600 mb-4" />
          <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2">Nenhum contato</h4>
          <p className="text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center">
            Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de referência.
          </p>
          <button
            onClick={() => handleOpenContactForm()}
            className="flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          >
            <UserPlus size={16} />
            <span>Adicionar Contato</span>
          </button>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
            <thead>
              <tr className="bg-neutral-50 dark:bg-gray-900">
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Nome
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Relacionamento
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Telefone
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700">
              {contacts.map(contact => (
                <tr key={contact.id} className="hover:bg-neutral-50 dark:hover:bg-gray-700">
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium">
                        {contact.name.charAt(0).toUpperCase()}
                      </div>
                      <span className="ml-2 text-neutral-700 dark:text-gray-200">{contact.name}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                      {contact.relationship || "Não especificado"}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    {contact.email ? (
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3 text-neutral-400 dark:text-gray-500" />
                        <a href={`mailto:${contact.email}`} className="text-primary-600 dark:text-primary-400 hover:underline">
                          {contact.email}
                        </a>
                      </div>
                    ) : (
                      <span className="text-neutral-400 dark:text-gray-500">Não informado</span>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    {contact.phone ? (
                      <div className="flex items-center gap-1">
                        <Phone className="h-3 w-3 text-neutral-400 dark:text-gray-500" />
                        <a href={`tel:${contact.phone}`} className="text-neutral-700 dark:text-gray-300">
                          {formatPhone(contact.phone)}
                        </a>
                      </div>
                    ) : (
                      <span className="text-neutral-400 dark:text-gray-500">Não informado</span>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-right">
                    <div className="flex justify-end">
                      <button
                        onClick={() => handleOpenContactForm(contact)}
                        className="p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                        title="Editar"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleViewNotes(contact)}
                        className={`p-1 ${contact.notes ? 'text-neutral-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400' : 'text-neutral-300 dark:text-gray-600 cursor-not-allowed'}`}
                        title={contact.notes ? "Ver Observações" : "Sem Observações"}
                        disabled={!contact.notes}
                      >
                        <FileText size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteContact(contact)}
                        className="p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                        title="Excluir"
                      >
                        <Trash size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Contact Form Modal */}
      {contactFormOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
          <div className="fixed inset-0 bg-black/50" onClick={handleCloseContactForm}></div>

          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[60]">
            <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-neutral-800 dark:text-gray-100">
                {selectedContact ? "Editar Contato" : "Novo Contato"}
              </h3>
              <button
                onClick={handleCloseContactForm}
                className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6">
              {formErrors.submit && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded-lg text-sm">
                  {formErrors.submit}
                </div>
              )}

              <form onSubmit={handleSubmitContact} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="name">
                    Nome *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="name"
                      name="name"
                      type="text"
                      value={formData.name}
                      onChange={handleFormChange}
                      className={`block w-full pl-10 pr-3 py-2 border ${formErrors.name ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'} rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                      placeholder="Nome do contato"
                      disabled={isSubmitting}
                    />
                  </div>
                  {formErrors.name && <p className="mt-1 text-xs text-red-600 dark:text-red-400">{formErrors.name}</p>}
                </div>

                <ModuleFormGroup
                  moduleColor="people"
                  label="Relacionamento"
                  htmlFor="relationship"
                  icon={<Users size={16} />}
                >
                  <ModuleSelect
                    moduleColor="people"
                    id="relationship"
                    name="relationship"
                    value={formData.relationship}
                    onChange={handleFormChange}
                    placeholder="Selecione o relacionamento"
                    disabled={isSubmitting}
                  >
                    <option value="">Selecione</option>
                    <option value="Cônjuge">Cônjuge</option>
                    <option value="Filho/Filha">Filho/Filha</option>
                    <option value="Pai/Mãe">Pai/Mãe</option>
                    <option value="Irmão/Irmã">Irmão/Irmã</option>
                    <option value="Avô/Avó">Avô/Avó</option>
                    <option value="Tio/Tia">Tio/Tia</option>
                    <option value="Primo/Prima">Primo/Prima</option>
                    <option value="Amigo">Amigo</option>
                    <option value="Colega">Colega</option>
                    <option value="Outro">Outro</option>
                  </ModuleSelect>
                </ModuleFormGroup>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="email">
                    Email
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleFormChange}
                      className={`block w-full pl-10 pr-3 py-2 border ${formErrors.email ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'} rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                      placeholder="<EMAIL>"
                      disabled={isSubmitting}
                    />
                  </div>
                  {formErrors.email && <p className="mt-1 text-xs text-red-600 dark:text-red-400">{formErrors.email}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="phone">
                    Telefone
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Phone className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <MaskedInput
                      type="phone"
                      value={formData.phone}
                      onChange={(e) =>
                        handleFormChange({
                          target: { name: "phone", value: e.target.value },
                        })
                      }
                      placeholder="(00) 00000-0000"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="notes">
                    Observações
                  </label>
                  <div className="relative">
                    <div className="absolute top-3 left-3 flex items-start pointer-events-none">
                      <FileText className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleFormChange}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none"
                      placeholder="Observações sobre o contato"
                      rows={3}
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 pt-2">
                  <button
                    type="button"
                    onClick={handleCloseContactForm}
                    className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
                    disabled={isSubmitting}
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 size={16} className="animate-spin" />
                        <span>Salvando...</span>
                      </>
                    ) : (
                      <span>Salvar</span>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Notes Modal */}
      {notesModalOpen && (
        <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
          <div className="fixed inset-0 bg-black/50" onClick={() => setNotesModalOpen(false)}></div>

          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[11050]">
            <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-neutral-800 dark:text-gray-100">
                Observações de {selectedNotes.name}
              </h3>
              <button
                onClick={() => setNotesModalOpen(false)}
                className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6">
              <div className="bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg border border-neutral-200 dark:border-gray-600 text-neutral-700 dark:text-gray-300 min-h-[100px] whitespace-pre-wrap">
                {selectedNotes.notes || "Nenhuma observação disponível."}
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => setNotesModalOpen(false)}
                  className="px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Fechar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => {
          setConfirmDialogOpen(false);
          setContactToDelete(null);
        }}
        onConfirm={confirmDeleteContact}
        title="Excluir Contato"
        message="Tem certeza que deseja excluir este contato? Esta ação não pode ser desfeita."
        variant="danger"
        confirmText="Excluir"
        cancelText="Cancelar"
      />
    </div>
  );
};

export default ContactsTab;