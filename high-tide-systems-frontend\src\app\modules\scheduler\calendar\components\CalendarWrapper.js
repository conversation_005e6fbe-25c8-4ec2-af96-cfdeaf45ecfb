"use client";

import React, { useEffect } from 'react';
import { Loader2 } from "lucide-react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import ptBrLocale from "@fullcalendar/core/locales/pt-br";
import { getDarkModeStyles, getEventWidthStyles } from '../utils/calendarStyles';

const CalendarWrapper = ({
  calendarRef,
  isDarkMode,
  isLoading,
  appointments,
  businessHours,
  handleDateSelect,
  handleEventClick,
  handleDatesSet,
  renderEventContent,
  handleSlotClassNames,
  canCreateAppointment,
  onShowMultipleEvents,
  customButtons,
  onExport
}) => {
  // Limpar todos os indicadores quando o componente for desmontado
  useEffect(() => {
    // Remover a variável CSS para altura fixa dos eventos
    document.documentElement.style.removeProperty('--fc-event-min-height');

    // Adicionar estilo global para animação
    const styleElement = document.createElement('style');
    styleElement.id = 'multiple-events-global-style';
    styleElement.textContent = `
      /* Permitir que eventos sobrepostos tenham altura proporcional à duração */
      .fc-event.has-overlapping-events {
        /* Não aplicar absolutamente nenhum estilo diferente */
      }

      /* Garantir que o FullCalendar calcule a altura dos eventos com base na duração */
      .fc-timegrid-event.has-overlapping-events {
        /* Remover restrições de altura para permitir tamanho proporcional */
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
      }

      /* Permitir que todos os eventos tenham altura proporcional à duração */
      .fc-timegrid-event {
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
      }

      /* Limitar a largura da coluna de eventos, deixando espaço à direita */
      .fc-timegrid-col-events {
        width: 90% !important;
        max-width: 90% !important;
        right: auto !important;
      }

      /* Garantir que não haja espaço entre eventos no mesmo horário */
      .fc-timegrid-event-harness {
        left: 0 !important;
      }

      .fc-timegrid-event-harness + .fc-timegrid-event-harness {
        margin-left: 0 !important;
        left: 0 !important;
      }

      /* Adicionar apenas um pequeno badge no canto superior direito */
      .multiple-events-badge {
        position: absolute !important;
        top: 2px !important;
        right: 2px !important;
        width: 18px !important;
        height: 18px !important;
        background-color: #9333ea !important; /* Roxo mais escuro (violet-600) diferente do status Pendente (#8b5cf6) */
        color: white !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 12px !important;
        font-weight: bold !important;
        line-height: 1 !important;
        z-index: 1000 !important; /* Valor alto para garantir que fique acima de outros elementos */
        cursor: pointer !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
        border: 1px solid white !important;
        pointer-events: auto !important; /* Garantir que o badge receba eventos de clique */
        margin: 0 !important; /* Remover margens */
        padding: 0 !important; /* Remover padding */
        transform: none !important; /* Remover transformações */
      }

      /* Garantir que o badge seja clicável e não interfira no evento principal */
      .multiple-events-badge:hover {
        /* Usar um efeito hover mais sutil que não afete o layout */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4) !important;
        filter: brightness(1.1) !important;
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      // Remover o estilo global
      const globalStyle = document.getElementById('multiple-events-global-style');
      if (globalStyle) {
        globalStyle.remove();
      }

      // Remover a variável CSS
      document.documentElement.style.removeProperty('--fc-event-min-height');

      // Remover badges e outros elementos
      const badges = document.querySelectorAll('.multiple-events-badge');
      badges.forEach(el => {
        el.remove();
      });

      // Remover outros indicadores antigos (compatibilidade)
      const indicators = document.querySelectorAll('button[class*="multiple-events-indicator-"]');
      indicators.forEach(el => {
        el.remove();
      });
    };
  }, []);
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-black/30">
      {/* Injetar estilos CSS para dark mode quando necessário */}
      {isDarkMode && <style>{getDarkModeStyles()}</style>}

      {/* Injetar estilos CSS para forçar a mesma largura em todos os eventos */}
      <style>{getEventWidthStyles()}</style>

      {/* Estilos para o botão de exportação */}
      <style>{`
        .fc-exportButton-button {
          background-color: #f97316 !important; /* Cor laranja do módulo scheduler */
          border-color: #ea580c !important;
          color: white !important;
          font-weight: 500 !important;
          padding: 0.375rem 0.75rem !important;
          border-radius: 0.375rem !important;
          margin-right: 0.5rem !important;
        }
        .fc-exportButton-button:hover {
          background-color: #ea580c !important;
          border-color: #c2410c !important;
        }
        .fc-exportButton-button:focus {
          box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.5) !important;
          outline: none !important;
        }
        .dark .fc-exportButton-button {
          background-color: #ea580c !important;
          border-color: #c2410c !important;
        }
        .dark .fc-exportButton-button:hover {
          background-color: #c2410c !important;
          border-color: #9a3412 !important;
        }
      `}</style>

      <div className={`relative rounded-lg overflow-hidden ${isLoading ? "opacity-50" : ""}`}>
        {isLoading && (
          <div className="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-50 dark:bg-opacity-50 flex items-center justify-center z-10">
            <Loader2 className="w-8 h-8 text-primary-500 dark:text-primary-400 animate-spin" />
          </div>
        )}

        <FullCalendar
          ref={calendarRef}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          locale={ptBrLocale}
          allDaySlot={false}
          buttonText={{
            today: "Hoje",
            month: "Mês",
            week: "Semana",
            day: "Dia",
          }}
          headerToolbar={{
            left: "prev,next today",
            center: "title",
            right: "exportButton dayGridMonth,timeGridWeek,timeGridDay",
          }}
          customButtons={{
            exportButton: {
              text: 'Exportar',
              click: function() {
                if (onExport) {
                  // Remover qualquer dropdown existente primeiro
                  const existingDropdown = document.getElementById('calendar-export-dropdown');
                  if (existingDropdown) {
                    existingDropdown.remove();
                  }

                  // Obter o botão de exportação
                  const button = document.querySelector('.fc-exportButton-button');
                  if (!button) return;

                  // Criar um container para o dropdown
                  const dropdownContainer = document.createElement('div');
                  dropdownContainer.id = 'calendar-export-dropdown-container';
                  dropdownContainer.style.position = 'absolute';
                  dropdownContainer.style.top = '0';
                  dropdownContainer.style.left = '0';
                  dropdownContainer.style.width = '100%';
                  dropdownContainer.style.height = '100%';
                  dropdownContainer.style.pointerEvents = 'none';
                  dropdownContainer.style.zIndex = '1000';

                  // Criar o dropdown
                  const dropdown = document.createElement('div');
                  dropdown.id = 'calendar-export-dropdown';
                  dropdown.style.position = 'absolute';
                  dropdown.style.pointerEvents = 'auto';
                  dropdown.style.backgroundColor = isDarkMode ? '#374151' : '#ffffff';
                  dropdown.style.border = isDarkMode ? '1px solid #4b5563' : '1px solid #e5e7eb';
                  dropdown.style.borderRadius = '0.375rem';
                  dropdown.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
                  dropdown.style.width = '150px';
                  dropdown.style.padding = '0.5rem 0';

                  // Posicionar o dropdown - esta é a parte crítica
                  const buttonRect = button.getBoundingClientRect();
                  const headerToolbar = button.closest('.fc-header-toolbar');

                  if (headerToolbar) {
                    // Posicionar o dropdown em relação ao header toolbar
                    const headerRect = headerToolbar.getBoundingClientRect();

                    // Calcular a posição do dropdown
                    // Posicionar o dropdown abaixo do botão
                    dropdown.style.top = `${buttonRect.bottom - headerRect.top + 5}px`;
                    // Alinhar com a esquerda do botão
                    dropdown.style.left = `${buttonRect.left - headerRect.left}px`;

                    // Adicionar o dropdown ao container
                    dropdownContainer.appendChild(dropdown);

                    // Adicionar o container ao header toolbar
                    headerToolbar.style.position = 'relative';
                    headerToolbar.appendChild(dropdownContainer);
                  } else {
                    // Fallback - adicionar ao body com posição fixa
                    dropdown.style.position = 'fixed';
                    dropdown.style.top = `${buttonRect.bottom + 5}px`;
                    dropdown.style.left = `${buttonRect.left}px`;
                    document.body.appendChild(dropdown);
                  }

                  // Opções de exportação
                  const options = [
                    { text: 'Imagem (PNG)', value: 'image' },
                    { text: 'PDF', value: 'pdf' },
                    { text: 'Excel (XLSX)', value: 'xlsx' }
                  ];

                  options.forEach(option => {
                    const item = document.createElement('div');
                    item.className = 'calendar-export-option';
                    item.textContent = option.text;
                    item.style.padding = '0.5rem 1rem';
                    item.style.cursor = 'pointer';
                    item.style.color = isDarkMode ? '#e5e7eb' : '#374151';
                    item.style.fontSize = '0.875rem';

                    item.addEventListener('mouseover', () => {
                      item.style.backgroundColor = isDarkMode ? '#4b5563' : '#f3f4f6';
                    });

                    item.addEventListener('mouseout', () => {
                      item.style.backgroundColor = 'transparent';
                    });

                    item.addEventListener('click', () => {
                      onExport(option.value);
                      dropdownContainer.remove();
                    });

                    dropdown.appendChild(item);
                  });

                  // Fechar dropdown ao clicar fora
                  const closeDropdown = (e) => {
                    if (!dropdown.contains(e.target) && e.target !== button) {
                      dropdownContainer.remove();
                      document.removeEventListener('click', closeDropdown);
                    }
                  };

                  // Usar setTimeout para evitar que o evento de clique atual feche o dropdown
                  setTimeout(() => {
                    document.addEventListener('click', closeDropdown);
                  }, 0);
                }
              }
            }
          }}
          views={{
            dayGridMonth: {
              // Configurações para a visualização de mês
              defaultTimedEventDuration: "01:00:00", // Duração padrão de 1 hora
            },
            timeGridWeek: {
              slotDuration: "00:30:00",
              slotLabelFormat: {
                hour: "2-digit",
                minute: "2-digit",
                hour12: false,
              },
              slotLabelInterval: "01:00",
              snapDuration: "01:00:00", // Alterado de 15 minutos para 1 hora
              defaultTimedEventDuration: "01:00:00", // Duração padrão de 1 hora
              nowIndicator: true,
              slotMinHeight: 55, // Aumentar ligeiramente o espaço entre os horários
            },
            timeGridDay: {
              // Configurações para a visualização de dia
              slotDuration: "00:30:00",
              slotLabelInterval: "01:00",
              snapDuration: "01:00:00", // Duração do snap de 1 hora
              defaultTimedEventDuration: "01:00:00", // Duração padrão de 1 hora
              nowIndicator: true,
              slotMinHeight: 55, // Aumentar ligeiramente o espaço entre os horários
            },
          }}
          // Configurações para eventos sobrepostos
          eventOverlap={true}
          // Não agrupar eventos sobrepostos
          eventOrder="start,-duration,title"
          // Permitir que eventos sobrepostos sejam exibidos lado a lado
          slotEventOverlap={false}
          // Permitir que eventos tenham altura proporcional à duração
          // Adicionar classes personalizadas aos eventos - abordagem simplificada
          eventClassNames={(arg) => {
            // Adicionar classes com base no tipo de visualização
            if (arg.view.type === 'timeGridWeek' || arg.view.type === 'timeGridDay') {
              // Verificar se há eventos no mesmo horário - abordagem mais simples
              const date = arg.event.start;
              const dateStr = date.toISOString().split('T')[0];
              const hour = date.getHours();
              const minute = date.getMinutes();

              // Encontrar eventos no mesmo horário exato
              const sameTimeEvents = appointments.filter(evt => {
                if (evt.id === arg.event.id) return false;

                const evtDate = new Date(evt.start);
                return (
                  evtDate.toISOString().split('T')[0] === dateStr &&
                  evtDate.getHours() === hour &&
                  evtDate.getMinutes() === minute
                );
              });

              // Adicionar classe especial para eventos sobrepostos
              if (sameTimeEvents.length > 0) {
                return ['calendar-timegrid-event', 'has-overlapping-events', 'multiple-events-indicator'];
              }

              return ['calendar-timegrid-event'];
            }
            return [];
          }}
          // Adicionar indicador de múltiplos eventos apenas no último evento sobreposto
          // e expandir eventos que não têm outros eventos seguidos
          eventDidMount={(info) => {
            // Verificar se estamos em visualização de semana ou dia
            if (info.view.type === 'timeGridWeek' || info.view.type === 'timeGridDay') {
              // Verificar se há eventos no mesmo horário
              const date = info.event.start;
              const dateStr = date.toISOString().split('T')[0];
              const hour = date.getHours();
              const minute = date.getMinutes();

              // Verificar se há eventos seguidos a este
              const hasFollowingEvents = appointments.some(evt => {
                if (evt.id === info.event.id) return false;

                const evtDate = new Date(evt.start);
                const evtDateStr = evtDate.toISOString().split('T')[0];

                // Verificar se é no mesmo dia e se começa exatamente quando este termina
                return (
                  evtDateStr === dateStr &&
                  evtDate.getHours() === (info.event.end ? info.event.end.getHours() : hour + 1) &&
                  evtDate.getMinutes() === (info.event.end ? info.event.end.getMinutes() : minute)
                );
              });

              // Se não houver eventos seguidos, expandir este evento para ocupar mais espaço
              if (!hasFollowingEvents && info.event.end) {
                // Calcular a duração atual em minutos
                const durationMinutes = (info.event.end - info.event.start) / (60 * 1000);

                // Se a duração for de 1 hora, expandir para 2 horas visualmente
                if (durationMinutes === 60) {
                  // Obter a altura de um slot de 1 hora
                  const slotHeight = info.view.type === 'timeGridWeek' ? 55 : 55; // Usar o mesmo valor de slotMinHeight

                  // Expandir para ocupar o espaço de 2 horas
                  info.el.style.height = `${slotHeight * 2}px`;
                  info.el.style.maxHeight = 'none';
                }
              }

              // Encontrar eventos no mesmo horário
              const sameTimeEvents = appointments.filter(evt => {
                if (evt.id === info.event.id) return false;

                const evtDate = new Date(evt.start);
                return (
                  evtDate.toISOString().split('T')[0] === dateStr &&
                  evtDate.getHours() === hour &&
                  evtDate.getMinutes() === minute
                );
              });

              // Se houver eventos sobrepostos
              if (sameTimeEvents.length > 0) {
                // Adicionar a classe sem modificar outros estilos
                info.el.classList.add('has-overlapping-events');

                // Não definir altura fixa para permitir tamanho proporcional à duração

                // Determinar se este é o último evento sobreposto
                // Primeiro, obter todos os eventos neste horário, incluindo o atual
                const allEvents = [...sameTimeEvents, {
                  id: info.event.id,
                  start: info.event.start,
                  // Extrair o timestamp do ID, se possível (assumindo que o ID contém um timestamp)
                  timestamp: info.event.id.match(/\d{13}/) ? parseInt(info.event.id.match(/\d{13}/)[0]) : 0
                }];

                // Tentar extrair timestamps dos IDs dos eventos para ordenação
                allEvents.forEach(evt => {
                  if (!evt.timestamp && typeof evt.id === 'string') {
                    // Tentar extrair um timestamp do ID (assumindo formato que contém timestamp)
                    const match = evt.id.match(/\d{13}/);
                    evt.timestamp = match ? parseInt(match[0]) : 0;
                  }
                });

                // Abordagem mais direta: adicionar o badge a todos os eventos e depois remover dos que não são o mais à direita

                // Primeiro, vamos adicionar um identificador temporário a este evento
                const currentEventId = info.event.id;

                // Vamos adicionar o badge a este evento
                const badge = document.createElement('div');
                badge.className = 'multiple-events-badge';
                badge.innerHTML = `${sameTimeEvents.length + 1}`;
                badge.title = "Clique para ver todos os eventos neste horário";
                badge.dataset.eventId = currentEventId; // Armazenar o ID do evento no badge
                badge.dataset.hour = `${hour}:${minute}`; // Armazenar o horário no badge
                badge.dataset.date = dateStr; // Armazenar a data no badge

                // Estilizar o badge
                badge.style.position = "absolute";
                badge.style.top = "2px";
                badge.style.right = "2px";
                badge.style.width = "18px";
                badge.style.height = "18px";
                badge.style.padding = "0";
                badge.style.margin = "0";
                badge.style.zIndex = "1000";
                badge.style.pointerEvents = "auto";
                badge.style.boxSizing = "content-box";
                badge.style.overflow = "visible";
                badge.style.backgroundColor = "#9333ea"; // Roxo mais escuro (violet-600)
                badge.style.color = "white";
                badge.style.borderRadius = "50%";
                badge.style.display = "flex";
                badge.style.alignItems = "center";
                badge.style.justifyContent = "center";
                badge.style.fontSize = "12px";
                badge.style.fontWeight = "bold";
                badge.style.lineHeight = "1";
                badge.style.boxShadow = "0 1px 3px rgba(0, 0, 0, 0.3)";
                badge.style.border = "1px solid white";

                // Adicionar evento de clique ao badge
                badge.addEventListener('click', (e) => {
                  // Parar a propagação do evento
                  e.stopPropagation();
                  e.preventDefault();

                  // Encontrar todos os eventos no mesmo horário
                  const allModalEvents = appointments.filter(evt => {
                    const evtDate = new Date(evt.start);
                    return (
                      evtDate.toISOString().split('T')[0] === dateStr &&
                      evtDate.getHours() === hour &&
                      evtDate.getMinutes() === minute
                    );
                  });

                  // Mostrar o modal com todos os eventos
                  if (onShowMultipleEvents) {
                    onShowMultipleEvents(date, allModalEvents);
                  }
                });

                // Adicionar o badge ao evento
                const eventContent = info.el.querySelector('.fc-event-main');
                if (eventContent) {
                  eventContent.style.position = 'relative';
                  eventContent.appendChild(badge);
                } else {
                  info.el.appendChild(badge);
                }

                // Definir um timeout para verificar e remover badges duplicados
                // Isso será executado após todos os eventos serem renderizados
                setTimeout(() => {
                  // Encontrar todos os badges para este horário específico
                  const allBadges = document.querySelectorAll(`.multiple-events-badge[data-date="${dateStr}"][data-hour="${hour}:${minute}"]`);

                  if (allBadges.length > 1) {
                    // Encontrar o evento mais à direita
                    let rightmostBadge = null;
                    let maxRight = -1;

                    allBadges.forEach(badgeEl => {
                      const rect = badgeEl.getBoundingClientRect();
                      if (rect.right > maxRight) {
                        maxRight = rect.right;
                        rightmostBadge = badgeEl;
                      }
                    });

                    // Remover todos os badges exceto o mais à direita
                    allBadges.forEach(badgeEl => {
                      if (badgeEl !== rightmostBadge) {
                        badgeEl.remove();
                      }
                    });
                  }
                }, 100);

                // Não precisamos do bloco if (isLastEvent) pois já adicionamos o badge acima
              }
            }
          }}
          slotMinTime="08:00:00"
          slotMaxTime="20:00:00"
          selectable={canCreateAppointment}
          selectMirror={true}
          defaultTimedEventDuration="01:00:00" // Definir duração padrão de eventos para 1 hora
          forceEventDuration={true} // Forçar a duração do evento
          dayMaxEvents={3}
          weekends={true} // Exibir sábado
          hiddenDays={[0]} // Esconder domingo (0 = domingo)
          events={appointments}
          select={(selectInfo) => {
            console.log('[CALENDAR-SELECT] Evento de seleção original:', {
              start: selectInfo.start?.toLocaleString(),
              end: selectInfo.end?.toLocaleString(),
              temEnd: !!selectInfo.end,
              duracaoMinutos: selectInfo.end ? (selectInfo.end - selectInfo.start) / (60 * 1000) : 'N/A'
            });
            handleDateSelect(selectInfo);
          }}
          eventClick={handleEventClick}
          eventContent={renderEventContent}
          height="auto"
          dayHeaderClassNames={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-neutral-700'} py-3`}
          slotLabelClassNames={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-neutral-600'}`}
          moreLinkContent={(args) => `+${args.num} agendamentos`}
          noEventsText="Nenhum agendamento"
          moreLinkClassNames={`text-xs font-medium ${isDarkMode ? 'text-primary-400 hover:text-primary-300' : 'text-primary-600 hover:text-primary-700'} hover:underline`}
          datesSet={(dateInfo) => {
            // Limpar todos os indicadores de múltiplos eventos quando a visualização mudar
            // Usar um seletor mais específico para garantir que pegamos todos os indicadores
            const indicators = document.querySelectorAll('button[class*="multiple-events-indicator-"]');
            indicators.forEach(el => {
              el.remove();
            });

            // Chamar o handler original
            handleDatesSet(dateInfo);
          }}
          businessHours={businessHours}
          businessHoursHighlight={true}
          businessHoursNonBusinessDaysClass={isDarkMode ? "bg-gray-700" : "bg-neutral-200"}
          businessHoursNonBusinessHoursClass={isDarkMode ? "bg-gray-700" : "bg-neutral-200"}
          slotLaneClassNames={handleSlotClassNames}
        />
      </div>
    </div>
  );
};

export default CalendarWrapper;