-- CreateTable
CREATE TABLE "BaseModel" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "BaseModel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProfessionGroup" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "companyId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "defaultModules" "SystemModule"[] DEFAULT ARRAY['BASIC']::"SystemModule"[],
    "defaultPermissions" TEXT[] DEFAULT ARRAY[]::TEXT[],

    CONSTRAINT "ProfessionGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Profession" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "groupId" TEXT,
    "companyId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "Profession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "address" TEXT,
    "birthDate" TIMESTAMP(3),
    "cnpj" TEXT,
    "cpf" TEXT,
    "createdById" TEXT,
    "fullName" TEXT NOT NULL,
    "login" TEXT NOT NULL,
    "modules" "SystemModule"[],
    "permissions" TEXT[],
    "phone" TEXT,
    "companyId" TEXT,
    "deletedAt" TIMESTAMP(3),
    "deletedById" TEXT,
    "failedLoginAttempts" INTEGER NOT NULL DEFAULT 0,
    "lastLoginAt" TIMESTAMP(3),
    "lastLoginIp" TEXT,
    "passwordChangedAt" TIMESTAMP(3),
    "role" "UserRole" NOT NULL DEFAULT 'EMPLOYEE',
    "professionId" TEXT,
    "profileImageUrl" TEXT,
    "city" TEXT,
    "postalCode" TEXT,
    "state" TEXT,
    "branchId" TEXT,
    "neighborhood" TEXT,
    "modulePreferences" JSONB,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Client" (
    "id" TEXT NOT NULL,
    "login" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdById" TEXT NOT NULL,
    "companyId" TEXT,
    "deletedAt" TIMESTAMP(3),
    "deletedById" TEXT,

    CONSTRAINT "Client_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Person" (
    "id" TEXT NOT NULL,
    "email" TEXT,
    "phone" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "birthDate" TIMESTAMP(3),
    "notes" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "neighborhood" TEXT,
    "postalCode" TEXT,
    "cpf" TEXT,
    "createdById" TEXT NOT NULL,
    "deletedById" TEXT,
    "fullName" TEXT NOT NULL,
    "gender" TEXT,
    "profileImageUrl" TEXT,
    "relationship" TEXT,
    "useClientEmail" BOOLEAN NOT NULL DEFAULT false,
    "useClientPhone" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Person_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Document" (
    "id" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "clientId" TEXT,
    "userId" TEXT,
    "companyId" TEXT,
    "createdById" TEXT,
    "externalUrl" TEXT,
    "mimeType" TEXT NOT NULL,
    "ownerType" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "personId" TEXT,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Scheduling" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "creatorId" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "serviceTypeId" TEXT NOT NULL,
    "insuranceId" TEXT,
    "status" "SchedulingStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "recurrenceId" TEXT,
    "companyId" TEXT,
    "confirmationSentAt" TIMESTAMP(3),
    "reminderSentAt" TIMESTAMP(3),
    "branchId" TEXT,

    CONSTRAINT "Scheduling_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Recurrence" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "clientId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "serviceTypeId" TEXT NOT NULL,
    "insuranceId" TEXT,
    "recurrenceType" "RecurrenceType" NOT NULL,
    "numberOfOccurrences" INTEGER,
    "endDate" TIMESTAMP(3),
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdById" TEXT NOT NULL,
    "companyId" TEXT,
    "branchId" TEXT,
    "personId" TEXT NOT NULL,

    CONSTRAINT "Recurrence_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RecurrencePattern" (
    "id" TEXT NOT NULL,
    "recurrenceId" TEXT NOT NULL,
    "dayOfWeek" INTEGER NOT NULL,
    "endTimeMinutes" INTEGER NOT NULL,
    "startTimeMinutes" INTEGER NOT NULL,

    CONSTRAINT "RecurrencePattern_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Location" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "phone" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "companyId" TEXT,
    "deletedAt" TIMESTAMP(3),
    "branchId" TEXT,

    CONSTRAINT "Location_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ServiceType" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "value" DECIMAL(10,2) NOT NULL,
    "companyId" TEXT,

    CONSTRAINT "ServiceType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Insurance" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "companyId" TEXT,

    CONSTRAINT "Insurance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkingHours" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "dayOfWeek" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "breakEndMinutes" INTEGER,
    "breakStartMinutes" INTEGER,
    "endTimeMinutes" INTEGER NOT NULL,
    "startTimeMinutes" INTEGER NOT NULL,

    CONSTRAINT "WorkingHours_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Company" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "tradingName" TEXT,
    "legalName" TEXT,
    "industry" TEXT,
    "contactEmail" TEXT,
    "cnpj" TEXT NOT NULL,
    "phone" TEXT,
    "privacyPolicyUrl" TEXT,
    "termsOfServiceUrl" TEXT,
    "phone2" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "postalCode" TEXT,
    "website" TEXT,
    "primaryColor" TEXT,
    "secondaryColor" TEXT,
    "description" TEXT,
    "socialMedia" JSONB,
    "businessHours" JSONB,
    "defaultCurrency" TEXT NOT NULL DEFAULT 'BRL',
    "timeZone" TEXT NOT NULL DEFAULT 'America/Sao_Paulo',
    "plan" TEXT,
    "licenseValidUntil" TIMESTAMP(3),
    "active" BOOLEAN NOT NULL DEFAULT true,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "stripeCustomerId" TEXT,

    CONSTRAINT "Company_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailConfig" (
    "id" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "smtpHost" TEXT NOT NULL,
    "smtpPort" INTEGER NOT NULL,
    "smtpSecure" BOOLEAN NOT NULL DEFAULT false,
    "smtpUser" TEXT NOT NULL,
    "smtpPassword" TEXT NOT NULL,
    "emailFromName" TEXT NOT NULL,
    "emailFromAddress" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Subscription" (
    "id" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "billingCycle" "BillingCycle" NOT NULL DEFAULT 'MONTHLY',
    "active" BOOLEAN NOT NULL DEFAULT true,
    "status" "SubscriptionStatus" NOT NULL DEFAULT 'ACTIVE',
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP(3),
    "trialEndDate" TIMESTAMP(3),
    "lastBillingDate" TIMESTAMP(3),
    "nextBillingDate" TIMESTAMP(3),
    "cancelAtPeriodEnd" BOOLEAN NOT NULL DEFAULT false,
    "stripeCustomerId" TEXT,
    "stripeSubscriptionId" TEXT,
    "stripeCurrentPeriodEnd" TIMESTAMP(3),
    "pricePerMonth" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "userLimit" INTEGER NOT NULL DEFAULT 50,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Subscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionModule" (
    "id" TEXT NOT NULL,
    "subscriptionId" TEXT NOT NULL,
    "moduleType" "SystemModule" NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "stripePriceId" TEXT,
    "pricePerMonth" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "addedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SubscriptionModule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Invoice" (
    "id" TEXT NOT NULL,
    "subscriptionId" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "status" "InvoiceStatus" NOT NULL DEFAULT 'PENDING',
    "dueDate" TIMESTAMP(3) NOT NULL,
    "paidAt" TIMESTAMP(3),
    "stripeInvoiceId" TEXT,
    "stripeInvoiceUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Invoice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuditLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "details" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "companyId" TEXT,

    CONSTRAINT "AuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PasswordReset" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "usedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PasswordReset_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Branch" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT,
    "description" TEXT,
    "address" TEXT NOT NULL,
    "city" TEXT,
    "state" TEXT,
    "postalCode" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "isHeadquarters" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "companyId" TEXT NOT NULL,
    "neighborhood" TEXT,
    "defaultWorkingHours" JSONB,

    CONSTRAINT "Branch_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Contact" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "relationship" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "personId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "deletedById" TEXT,

    CONSTRAINT "Contact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClientInsurance" (
    "clientId" TEXT NOT NULL,
    "insuranceId" TEXT NOT NULL,
    "notes" TEXT,
    "policyNumber" TEXT,
    "validUntil" TIMESTAMP(3),

    CONSTRAINT "ClientInsurance_pkey" PRIMARY KEY ("clientId","insuranceId")
);

-- CreateTable
CREATE TABLE "PersonInsurance" (
    "personId" TEXT NOT NULL,
    "insuranceId" TEXT NOT NULL,
    "policyNumber" TEXT,
    "validUntil" TIMESTAMP(3),
    "notes" TEXT,

    CONSTRAINT "PersonInsurance_pkey" PRIMARY KEY ("personId","insuranceId")
);

-- CreateTable
CREATE TABLE "PersonInsuranceServiceLimit" (
    "id" TEXT NOT NULL,
    "personId" TEXT NOT NULL,
    "insuranceId" TEXT NOT NULL,
    "serviceTypeId" TEXT NOT NULL,
    "monthlyLimit" INTEGER NOT NULL,
    "yearlyLimit" INTEGER NOT NULL DEFAULT 0,
    "notes" TEXT,

    CONSTRAINT "PersonInsuranceServiceLimit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Conversation" (
    "id" TEXT NOT NULL,
    "type" "ConversationType" NOT NULL,
    "title" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "companyId" TEXT NOT NULL,
    "branchId" TEXT,
    "lastMessageAt" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "Conversation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ConversationParticipant" (
    "id" TEXT NOT NULL,
    "conversationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "leftAt" TIMESTAMP(3),
    "isAdmin" BOOLEAN NOT NULL DEFAULT false,
    "lastReadMessageId" TEXT,

    CONSTRAINT "ConversationParticipant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Message" (
    "id" TEXT NOT NULL,
    "conversationId" TEXT NOT NULL,
    "senderId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "contentType" "MessageContentType" NOT NULL DEFAULT 'TEXT',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "referencedMessageId" TEXT,
    "metadata" JSONB,

    CONSTRAINT "Message_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MessageStatus" (
    "id" TEXT NOT NULL,
    "messageId" TEXT NOT NULL,
    "participantId" TEXT NOT NULL,
    "status" "MessageDeliveryStatus" NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MessageStatus_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Evaluation" (
    "id" TEXT NOT NULL,
    "type" "EvaluationType" NOT NULL,
    "name" TEXT NOT NULL,
    "observations" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "Evaluation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Level" (
    "id" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "ageRange" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "evaluationId" TEXT NOT NULL,

    CONSTRAINT "Level_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Skill" (
    "id" TEXT NOT NULL,
    "code" TEXT,
    "order" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,

    CONSTRAINT "Skill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EvaluationSkill" (
    "evaluationId" TEXT NOT NULL,
    "skillId" TEXT NOT NULL,

    CONSTRAINT "EvaluationSkill_pkey" PRIMARY KEY ("evaluationId","skillId")
);

-- CreateTable
CREATE TABLE "Score" (
    "id" TEXT NOT NULL,
    "type" "ScoreType" NOT NULL,
    "value" TEXT,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "evaluationId" TEXT NOT NULL,

    CONSTRAINT "Score_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Task" (
    "id" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "milestone" TEXT,
    "item" TEXT,
    "question" TEXT,
    "example" TEXT,
    "criteria" TEXT,
    "objective" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "evaluationId" TEXT NOT NULL,
    "skillId" TEXT,
    "levelId" TEXT,

    CONSTRAINT "Task_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StandardCriteria" (
    "id" TEXT NOT NULL,
    "teachingType" "TeachingType" NOT NULL,
    "acronym" TEXT NOT NULL,
    "degree" "CriteriaDegree" NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,

    CONSTRAINT "StandardCriteria_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Program" (
    "id" TEXT NOT NULL,
    "type" "ProgramType" NOT NULL,
    "name" TEXT NOT NULL,
    "protocol" TEXT,
    "skill" TEXT,
    "milestone" TEXT,
    "teachingType" TEXT,
    "targetsPerSession" INTEGER DEFAULT 1,
    "attemptsPerTarget" INTEGER DEFAULT 1,
    "teachingProcedure" TEXT DEFAULT '',
    "instruction" TEXT DEFAULT '',
    "objective" TEXT DEFAULT '',
    "correctionProcedure" TEXT DEFAULT '',
    "learningCriteria" TEXT DEFAULT '',
    "materials" TEXT DEFAULT '',
    "notes" TEXT DEFAULT '',
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "promptStep" TEXT DEFAULT '',

    CONSTRAINT "Program_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProgramTarget" (
    "id" TEXT NOT NULL,
    "target" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "group" TEXT,
    "situation" TEXT DEFAULT 'ACTIVE',
    "startDate" TIMESTAMP(3),
    "acquisitionDate" TIMESTAMP(3),
    "maintenanceCount" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "programId" TEXT NOT NULL,

    CONSTRAINT "ProgramTarget_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CurriculumFolder" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "personId" TEXT NOT NULL,
    "shareWithParents" BOOLEAN NOT NULL DEFAULT false,
    "shareWithSchools" BOOLEAN NOT NULL DEFAULT false,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "CurriculumFolder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CurriculumFolderProgram" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "protocol" TEXT,
    "skill" TEXT,
    "milestone" TEXT,
    "teachingType" TEXT,
    "targetsPerSession" INTEGER DEFAULT 1,
    "attemptsPerTarget" INTEGER DEFAULT 1,
    "teachingProcedure" TEXT DEFAULT '',
    "instruction" TEXT DEFAULT '',
    "objective" TEXT DEFAULT '',
    "promptStep" TEXT DEFAULT '',
    "correctionProcedure" TEXT DEFAULT '',
    "learningCriteria" TEXT DEFAULT '',
    "materials" TEXT DEFAULT '',
    "notes" TEXT DEFAULT '',
    "status" "ProgramStatus" NOT NULL DEFAULT 'unallocated',
    "originalProgramId" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "curriculumFolderId" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "CurriculumFolderProgram_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Anamnese" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "personId" TEXT NOT NULL,
    "diagnostico" TEXT,
    "cuidador" TEXT,
    "profissaoCuidador" TEXT,
    "telefone" TEXT,
    "historicoPersonal" TEXT,
    "patologiaAssociada" TEXT,
    "convulsoes" TEXT,
    "sentou" TEXT,
    "engatinhou" TEXT,
    "andou" TEXT,
    "estereotipiasMotoras" TEXT,
    "alimentacaoSolidos" BOOLEAN NOT NULL DEFAULT false,
    "alimentacaoLiquidos" BOOLEAN NOT NULL DEFAULT false,
    "alimentacaoPastosos" BOOLEAN NOT NULL DEFAULT false,
    "alergiasIntolerancias" TEXT,
    "historicoMedico" TEXT,
    "medicacoes" TEXT,
    "alergias" TEXT,
    "historicoFamiliar" TEXT,
    "avdAlimentacao" TEXT,
    "avdBanho" TEXT,
    "avdVestuario" TEXT,
    "avdCuidadosPessoais" TEXT,
    "avdSono" TEXT,
    "avdEsfincter" TEXT,
    "gestosElementares" "SimNaoAsVezes" DEFAULT 'NAO',
    "naoSimbolicosConvencionais" "SimNaoAsVezes" DEFAULT 'NAO',
    "simbolicosRepresentacao" "SimNaoAsVezes" DEFAULT 'NAO',
    "verbal" "SimNaoAsVezes" DEFAULT 'NAO',
    "balbucio" "SimNaoAsVezes" DEFAULT 'NAO',
    "palavrasIsoladas" "SimNaoAsVezes" DEFAULT 'NAO',
    "quaisPalavrasIsoladas" TEXT,
    "enunciadoDuasPalavras" "SimNaoAsVezes" DEFAULT 'NAO',
    "frases" "SimNaoAsVezes" DEFAULT 'NAO',
    "estereotipiasVocais" "SimNaoAsVezes" DEFAULT 'NAO',
    "quaisEstereotipiasVocais" TEXT,
    "faltaExpressaoFacialAdequada" "SimNaoAsVezes" DEFAULT 'NAO',
    "apresentaAtencaoDiminuida" "SimNaoAsVezes" DEFAULT 'NAO',
    "apresentaPreferenciaIsolamento" "SimNaoAsVezes" DEFAULT 'NAO',
    "ageComoSeFosseSurdo" "SimNaoAsVezes" DEFAULT 'NAO',
    "olhaParaAlguemQueLheFala" "SimNaoAsVezes" DEFAULT 'NAO',
    "olhaQuandoChamadoPeloNome" "SimNaoAsVezes" DEFAULT 'NAO',
    "fazPedidoItensInteresse" "SimNaoAsVezes" DEFAULT 'NAO',
    "realizaImitacao" "SimNaoAsVezes" DEFAULT 'NAO',
    "brincaAdequadamenteBrinquedo" "SimNaoAsVezes" DEFAULT 'NAO',
    "preferenciasObjetosEspecificos" TEXT,
    "apresentaAversoes" TEXT,
    "autoEstimulacao" BOOLEAN DEFAULT false,
    "apresentaAutoAgressaoHeteroAgressao" BOOLEAN DEFAULT false,
    "apresentaBirrasIrritabilidade" BOOLEAN DEFAULT false,
    "apresentaManiasRituais" BOOLEAN DEFAULT false,
    "estuda" BOOLEAN DEFAULT false,
    "nomeEscola" TEXT,
    "serie" TEXT,
    "escolaRegular" BOOLEAN DEFAULT false,
    "professorApoio" BOOLEAN DEFAULT false,
    "outroCasoFamilia" BOOLEAN DEFAULT false,
    "outrosCasosDetalhamento" TEXT,
    "terapias" TEXT,
    "expectativasFamilia" TEXT,
    "observacoesGerais" TEXT,
    "versao" INTEGER NOT NULL DEFAULT 1,
    "ultimaAtualizacaoPorId" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "Anamnese_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EvolucaoDiaria" (
    "id" TEXT NOT NULL,
    "personId" TEXT NOT NULL,
    "profissionalId" TEXT NOT NULL,
    "dataInicio" TIMESTAMP(3) NOT NULL,
    "dataFim" TIMESTAMP(3) NOT NULL,
    "faltou" BOOLEAN NOT NULL DEFAULT false,
    "permitirVisualizacao" BOOLEAN NOT NULL DEFAULT false,
    "atendimento" TEXT,
    "observacoes" TEXT,
    "status" "StatusEvolucaoDiaria" NOT NULL DEFAULT 'RASCUNHO',
    "versao" INTEGER NOT NULL DEFAULT 1,
    "ultimaAtualizacaoPorId" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "EvolucaoDiaria_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ArquivoEvolucaoDiaria" (
    "id" TEXT NOT NULL,
    "evolucaoDiariaId" TEXT NOT NULL,
    "nome" TEXT NOT NULL,
    "tipo" TEXT NOT NULL,
    "tamanho" INTEGER NOT NULL,
    "url" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "ArquivoEvolucaoDiaria_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClientPerson" (
    "id" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "personId" TEXT NOT NULL,
    "relationship" TEXT,
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClientPerson_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_PersonToScheduling" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_PersonToScheduling_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "ProfessionGroup_companyId_idx" ON "ProfessionGroup"("companyId");

-- CreateIndex
CREATE INDEX "ProfessionGroup_active_idx" ON "ProfessionGroup"("active");

-- CreateIndex
CREATE INDEX "ProfessionGroup_deletedAt_idx" ON "ProfessionGroup"("deletedAt");

-- CreateIndex
CREATE INDEX "ProfessionGroup_name_idx" ON "ProfessionGroup"("name");

-- CreateIndex
CREATE INDEX "Profession_companyId_idx" ON "Profession"("companyId");

-- CreateIndex
CREATE INDEX "Profession_groupId_idx" ON "Profession"("groupId");

-- CreateIndex
CREATE INDEX "Profession_active_idx" ON "Profession"("active");

-- CreateIndex
CREATE INDEX "Profession_deletedAt_idx" ON "Profession"("deletedAt");

-- CreateIndex
CREATE INDEX "Profession_name_idx" ON "Profession"("name");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_cnpj_key" ON "User"("cnpj");

-- CreateIndex
CREATE UNIQUE INDEX "User_cpf_key" ON "User"("cpf");

-- CreateIndex
CREATE UNIQUE INDEX "User_login_key" ON "User"("login");

-- CreateIndex
CREATE INDEX "User_companyId_idx" ON "User"("companyId");

-- CreateIndex
CREATE INDEX "User_branchId_idx" ON "User"("branchId");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE INDEX "User_login_idx" ON "User"("login");

-- CreateIndex
CREATE INDEX "User_role_idx" ON "User"("role");

-- CreateIndex
CREATE INDEX "User_companyId_active_idx" ON "User"("companyId", "active");

-- CreateIndex
CREATE INDEX "User_companyId_role_idx" ON "User"("companyId", "role");

-- CreateIndex
CREATE INDEX "User_active_lastLoginAt_idx" ON "User"("active", "lastLoginAt");

-- CreateIndex
CREATE INDEX "User_companyId_active_role_idx" ON "User"("companyId", "active", "role");

-- CreateIndex
CREATE INDEX "User_fullName_idx" ON "User"("fullName");

-- CreateIndex
CREATE INDEX "User_professionId_companyId_idx" ON "User"("professionId", "companyId");

-- CreateIndex
CREATE UNIQUE INDEX "Client_login_key" ON "Client"("login");

-- CreateIndex
CREATE UNIQUE INDEX "Client_email_key" ON "Client"("email");

-- CreateIndex
CREATE INDEX "Client_companyId_idx" ON "Client"("companyId");

-- CreateIndex
CREATE INDEX "Client_email_idx" ON "Client"("email");

-- CreateIndex
CREATE INDEX "Client_login_idx" ON "Client"("login");

-- CreateIndex
CREATE UNIQUE INDEX "Person_cpf_key" ON "Person"("cpf");

-- CreateIndex
CREATE INDEX "Person_fullName_idx" ON "Person"("fullName");

-- CreateIndex
CREATE INDEX "Person_cpf_idx" ON "Person"("cpf");

-- CreateIndex
CREATE INDEX "Person_active_fullName_idx" ON "Person"("active", "fullName");

-- CreateIndex
CREATE INDEX "Person_createdById_idx" ON "Person"("createdById");

-- CreateIndex
CREATE INDEX "Person_active_createdAt_idx" ON "Person"("active", "createdAt");

-- CreateIndex
CREATE INDEX "Person_email_idx" ON "Person"("email");

-- CreateIndex
CREATE INDEX "Person_phone_idx" ON "Person"("phone");

-- CreateIndex
CREATE INDEX "Document_personId_idx" ON "Document"("personId");

-- CreateIndex
CREATE INDEX "Document_type_idx" ON "Document"("type");

-- CreateIndex
CREATE INDEX "Document_clientId_idx" ON "Document"("clientId");

-- CreateIndex
CREATE INDEX "Document_companyId_idx" ON "Document"("companyId");

-- CreateIndex
CREATE INDEX "Document_ownerType_clientId_idx" ON "Document"("ownerType", "clientId");

-- CreateIndex
CREATE INDEX "Document_ownerType_companyId_idx" ON "Document"("ownerType", "companyId");

-- CreateIndex
CREATE INDEX "Document_ownerType_userId_idx" ON "Document"("ownerType", "userId");

-- CreateIndex
CREATE INDEX "Document_userId_idx" ON "Document"("userId");

-- CreateIndex
CREATE INDEX "Scheduling_companyId_idx" ON "Scheduling"("companyId");

-- CreateIndex
CREATE INDEX "Scheduling_branchId_idx" ON "Scheduling"("branchId");

-- CreateIndex
CREATE INDEX "Scheduling_userId_startDate_idx" ON "Scheduling"("userId", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_status_startDate_idx" ON "Scheduling"("status", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_locationId_startDate_idx" ON "Scheduling"("locationId", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_serviceTypeId_idx" ON "Scheduling"("serviceTypeId");

-- CreateIndex
CREATE INDEX "Scheduling_clientId_startDate_idx" ON "Scheduling"("clientId", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_companyId_startDate_idx" ON "Scheduling"("companyId", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_companyId_status_startDate_idx" ON "Scheduling"("companyId", "status", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_userId_status_startDate_idx" ON "Scheduling"("userId", "status", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_serviceTypeId_startDate_status_idx" ON "Scheduling"("serviceTypeId", "startDate", "status");

-- CreateIndex
CREATE INDEX "Scheduling_locationId_status_startDate_idx" ON "Scheduling"("locationId", "status", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_insuranceId_startDate_idx" ON "Scheduling"("insuranceId", "startDate");

-- CreateIndex
CREATE INDEX "Scheduling_startDate_endDate_idx" ON "Scheduling"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "Scheduling_companyId_userId_startDate_idx" ON "Scheduling"("companyId", "userId", "startDate");

-- CreateIndex
CREATE INDEX "Recurrence_companyId_idx" ON "Recurrence"("companyId");

-- CreateIndex
CREATE INDEX "Recurrence_branchId_idx" ON "Recurrence"("branchId");

-- CreateIndex
CREATE INDEX "Recurrence_personId_idx" ON "Recurrence"("personId");

-- CreateIndex
CREATE INDEX "Recurrence_userId_idx" ON "Recurrence"("userId");

-- CreateIndex
CREATE INDEX "Recurrence_active_idx" ON "Recurrence"("active");

-- CreateIndex
CREATE INDEX "Recurrence_clientId_idx" ON "Recurrence"("clientId");

-- CreateIndex
CREATE INDEX "RecurrencePattern_recurrenceId_idx" ON "RecurrencePattern"("recurrenceId");

-- CreateIndex
CREATE INDEX "RecurrencePattern_dayOfWeek_idx" ON "RecurrencePattern"("dayOfWeek");

-- CreateIndex
CREATE INDEX "Location_companyId_idx" ON "Location"("companyId");

-- CreateIndex
CREATE INDEX "Location_active_idx" ON "Location"("active");

-- CreateIndex
CREATE INDEX "Location_branchId_idx" ON "Location"("branchId");

-- CreateIndex
CREATE INDEX "ServiceType_companyId_idx" ON "ServiceType"("companyId");

-- CreateIndex
CREATE UNIQUE INDEX "ServiceType_name_companyId_key" ON "ServiceType"("name", "companyId");

-- CreateIndex
CREATE INDEX "Insurance_companyId_idx" ON "Insurance"("companyId");

-- CreateIndex
CREATE UNIQUE INDEX "Insurance_name_companyId_key" ON "Insurance"("name", "companyId");

-- CreateIndex
CREATE INDEX "WorkingHours_userId_dayOfWeek_idx" ON "WorkingHours"("userId", "dayOfWeek");

-- CreateIndex
CREATE INDEX "WorkingHours_isActive_idx" ON "WorkingHours"("isActive");

-- CreateIndex
CREATE UNIQUE INDEX "Company_cnpj_key" ON "Company"("cnpj");

-- CreateIndex
CREATE INDEX "Company_active_idx" ON "Company"("active");

-- CreateIndex
CREATE INDEX "EmailConfig_companyId_idx" ON "EmailConfig"("companyId");

-- CreateIndex
CREATE INDEX "EmailConfig_active_idx" ON "EmailConfig"("active");

-- CreateIndex
CREATE UNIQUE INDEX "Subscription_companyId_key" ON "Subscription"("companyId");

-- CreateIndex
CREATE INDEX "Subscription_status_idx" ON "Subscription"("status");

-- CreateIndex
CREATE INDEX "Subscription_active_idx" ON "Subscription"("active");

-- CreateIndex
CREATE INDEX "Subscription_stripeCustomerId_idx" ON "Subscription"("stripeCustomerId");

-- CreateIndex
CREATE INDEX "Subscription_stripeSubscriptionId_idx" ON "Subscription"("stripeSubscriptionId");

-- CreateIndex
CREATE INDEX "SubscriptionModule_moduleType_idx" ON "SubscriptionModule"("moduleType");

-- CreateIndex
CREATE UNIQUE INDEX "SubscriptionModule_subscriptionId_moduleType_key" ON "SubscriptionModule"("subscriptionId", "moduleType");

-- CreateIndex
CREATE INDEX "Invoice_companyId_idx" ON "Invoice"("companyId");

-- CreateIndex
CREATE INDEX "Invoice_subscriptionId_idx" ON "Invoice"("subscriptionId");

-- CreateIndex
CREATE INDEX "Invoice_status_idx" ON "Invoice"("status");

-- CreateIndex
CREATE INDEX "AuditLog_userId_idx" ON "AuditLog"("userId");

-- CreateIndex
CREATE INDEX "AuditLog_entityType_entityId_idx" ON "AuditLog"("entityType", "entityId");

-- CreateIndex
CREATE INDEX "AuditLog_companyId_idx" ON "AuditLog"("companyId");

-- CreateIndex
CREATE INDEX "AuditLog_createdAt_idx" ON "AuditLog"("createdAt");

-- CreateIndex
CREATE INDEX "AuditLog_companyId_createdAt_idx" ON "AuditLog"("companyId", "createdAt");

-- CreateIndex
CREATE INDEX "AuditLog_action_createdAt_idx" ON "AuditLog"("action", "createdAt");

-- CreateIndex
CREATE INDEX "AuditLog_entityType_createdAt_idx" ON "AuditLog"("entityType", "createdAt");

-- CreateIndex
CREATE INDEX "AuditLog_companyId_action_createdAt_idx" ON "AuditLog"("companyId", "action", "createdAt");

-- CreateIndex
CREATE INDEX "AuditLog_userId_createdAt_idx" ON "AuditLog"("userId", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "PasswordReset_token_key" ON "PasswordReset"("token");

-- CreateIndex
CREATE INDEX "PasswordReset_token_idx" ON "PasswordReset"("token");

-- CreateIndex
CREATE INDEX "PasswordReset_userId_idx" ON "PasswordReset"("userId");

-- CreateIndex
CREATE INDEX "PasswordReset_expiresAt_idx" ON "PasswordReset"("expiresAt");

-- CreateIndex
CREATE INDEX "Branch_companyId_idx" ON "Branch"("companyId");

-- CreateIndex
CREATE INDEX "Branch_active_idx" ON "Branch"("active");

-- CreateIndex
CREATE UNIQUE INDEX "Branch_companyId_code_key" ON "Branch"("companyId", "code");

-- CreateIndex
CREATE INDEX "Contact_personId_idx" ON "Contact"("personId");

-- CreateIndex
CREATE INDEX "ClientInsurance_insuranceId_idx" ON "ClientInsurance"("insuranceId");

-- CreateIndex
CREATE INDEX "PersonInsurance_insuranceId_idx" ON "PersonInsurance"("insuranceId");

-- CreateIndex
CREATE INDEX "PersonInsuranceServiceLimit_personId_insuranceId_idx" ON "PersonInsuranceServiceLimit"("personId", "insuranceId");

-- CreateIndex
CREATE INDEX "PersonInsuranceServiceLimit_serviceTypeId_idx" ON "PersonInsuranceServiceLimit"("serviceTypeId");

-- CreateIndex
CREATE UNIQUE INDEX "PersonInsuranceServiceLimit_personId_insuranceId_serviceTyp_key" ON "PersonInsuranceServiceLimit"("personId", "insuranceId", "serviceTypeId");

-- CreateIndex
CREATE INDEX "Conversation_companyId_idx" ON "Conversation"("companyId");

-- CreateIndex
CREATE INDEX "Conversation_branchId_idx" ON "Conversation"("branchId");

-- CreateIndex
CREATE INDEX "Conversation_isActive_idx" ON "Conversation"("isActive");

-- CreateIndex
CREATE INDEX "Conversation_lastMessageAt_idx" ON "Conversation"("lastMessageAt");

-- CreateIndex
CREATE INDEX "ConversationParticipant_userId_idx" ON "ConversationParticipant"("userId");

-- CreateIndex
CREATE INDEX "ConversationParticipant_conversationId_idx" ON "ConversationParticipant"("conversationId");

-- CreateIndex
CREATE UNIQUE INDEX "ConversationParticipant_conversationId_userId_key" ON "ConversationParticipant"("conversationId", "userId");

-- CreateIndex
CREATE INDEX "Message_conversationId_idx" ON "Message"("conversationId");

-- CreateIndex
CREATE INDEX "Message_senderId_idx" ON "Message"("senderId");

-- CreateIndex
CREATE INDEX "Message_createdAt_idx" ON "Message"("createdAt");

-- CreateIndex
CREATE INDEX "Message_conversationId_createdAt_idx" ON "Message"("conversationId", "createdAt");

-- CreateIndex
CREATE INDEX "MessageStatus_messageId_idx" ON "MessageStatus"("messageId");

-- CreateIndex
CREATE INDEX "MessageStatus_participantId_idx" ON "MessageStatus"("participantId");

-- CreateIndex
CREATE UNIQUE INDEX "MessageStatus_messageId_participantId_key" ON "MessageStatus"("messageId", "participantId");

-- CreateIndex
CREATE INDEX "Evaluation_companyId_idx" ON "Evaluation"("companyId");

-- CreateIndex
CREATE INDEX "Evaluation_active_idx" ON "Evaluation"("active");

-- CreateIndex
CREATE INDEX "Evaluation_createdById_idx" ON "Evaluation"("createdById");

-- CreateIndex
CREATE INDEX "Evaluation_type_idx" ON "Evaluation"("type");

-- CreateIndex
CREATE INDEX "Level_evaluationId_idx" ON "Level"("evaluationId");

-- CreateIndex
CREATE INDEX "Level_order_idx" ON "Level"("order");

-- CreateIndex
CREATE INDEX "Skill_companyId_idx" ON "Skill"("companyId");

-- CreateIndex
CREATE INDEX "Skill_active_idx" ON "Skill"("active");

-- CreateIndex
CREATE INDEX "Skill_order_idx" ON "Skill"("order");

-- CreateIndex
CREATE INDEX "EvaluationSkill_evaluationId_idx" ON "EvaluationSkill"("evaluationId");

-- CreateIndex
CREATE INDEX "EvaluationSkill_skillId_idx" ON "EvaluationSkill"("skillId");

-- CreateIndex
CREATE INDEX "Score_evaluationId_idx" ON "Score"("evaluationId");

-- CreateIndex
CREATE INDEX "Score_type_idx" ON "Score"("type");

-- CreateIndex
CREATE INDEX "Task_evaluationId_idx" ON "Task"("evaluationId");

-- CreateIndex
CREATE INDEX "Task_skillId_idx" ON "Task"("skillId");

-- CreateIndex
CREATE INDEX "Task_levelId_idx" ON "Task"("levelId");

-- CreateIndex
CREATE INDEX "Task_order_idx" ON "Task"("order");

-- CreateIndex
CREATE INDEX "StandardCriteria_companyId_idx" ON "StandardCriteria"("companyId");

-- CreateIndex
CREATE INDEX "StandardCriteria_active_idx" ON "StandardCriteria"("active");

-- CreateIndex
CREATE INDEX "StandardCriteria_teachingType_idx" ON "StandardCriteria"("teachingType");

-- CreateIndex
CREATE INDEX "StandardCriteria_degree_idx" ON "StandardCriteria"("degree");

-- CreateIndex
CREATE INDEX "Program_companyId_idx" ON "Program"("companyId");

-- CreateIndex
CREATE INDEX "Program_active_idx" ON "Program"("active");

-- CreateIndex
CREATE INDEX "Program_type_idx" ON "Program"("type");

-- CreateIndex
CREATE INDEX "Program_createdById_idx" ON "Program"("createdById");

-- CreateIndex
CREATE INDEX "ProgramTarget_programId_idx" ON "ProgramTarget"("programId");

-- CreateIndex
CREATE INDEX "ProgramTarget_order_idx" ON "ProgramTarget"("order");

-- CreateIndex
CREATE INDEX "CurriculumFolder_companyId_idx" ON "CurriculumFolder"("companyId");

-- CreateIndex
CREATE INDEX "CurriculumFolder_active_idx" ON "CurriculumFolder"("active");

-- CreateIndex
CREATE INDEX "CurriculumFolder_personId_idx" ON "CurriculumFolder"("personId");

-- CreateIndex
CREATE INDEX "CurriculumFolder_createdById_idx" ON "CurriculumFolder"("createdById");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_curriculumFolderId_idx" ON "CurriculumFolderProgram"("curriculumFolderId");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_companyId_idx" ON "CurriculumFolderProgram"("companyId");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_active_idx" ON "CurriculumFolderProgram"("active");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_status_idx" ON "CurriculumFolderProgram"("status");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_createdById_idx" ON "CurriculumFolderProgram"("createdById");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_originalProgramId_idx" ON "CurriculumFolderProgram"("originalProgramId");

-- CreateIndex
CREATE INDEX "Anamnese_personId_idx" ON "Anamnese"("personId");

-- CreateIndex
CREATE INDEX "Anamnese_companyId_idx" ON "Anamnese"("companyId");

-- CreateIndex
CREATE INDEX "Anamnese_active_idx" ON "Anamnese"("active");

-- CreateIndex
CREATE INDEX "Anamnese_createdById_idx" ON "Anamnese"("createdById");

-- CreateIndex
CREATE INDEX "Anamnese_ultimaAtualizacaoPorId_idx" ON "Anamnese"("ultimaAtualizacaoPorId");

-- CreateIndex
CREATE INDEX "Anamnese_versao_idx" ON "Anamnese"("versao");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_personId_idx" ON "EvolucaoDiaria"("personId");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_profissionalId_idx" ON "EvolucaoDiaria"("profissionalId");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_companyId_idx" ON "EvolucaoDiaria"("companyId");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_active_idx" ON "EvolucaoDiaria"("active");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_createdById_idx" ON "EvolucaoDiaria"("createdById");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_ultimaAtualizacaoPorId_idx" ON "EvolucaoDiaria"("ultimaAtualizacaoPorId");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_status_idx" ON "EvolucaoDiaria"("status");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_dataInicio_idx" ON "EvolucaoDiaria"("dataInicio");

-- CreateIndex
CREATE INDEX "EvolucaoDiaria_faltou_idx" ON "EvolucaoDiaria"("faltou");

-- CreateIndex
CREATE INDEX "ArquivoEvolucaoDiaria_evolucaoDiariaId_idx" ON "ArquivoEvolucaoDiaria"("evolucaoDiariaId");

-- CreateIndex
CREATE INDEX "ArquivoEvolucaoDiaria_companyId_idx" ON "ArquivoEvolucaoDiaria"("companyId");

-- CreateIndex
CREATE INDEX "ArquivoEvolucaoDiaria_createdById_idx" ON "ArquivoEvolucaoDiaria"("createdById");

-- CreateIndex
CREATE INDEX "ClientPerson_clientId_idx" ON "ClientPerson"("clientId");

-- CreateIndex
CREATE INDEX "ClientPerson_personId_idx" ON "ClientPerson"("personId");

-- CreateIndex
CREATE INDEX "ClientPerson_personId_isPrimary_idx" ON "ClientPerson"("personId", "isPrimary");

-- CreateIndex
CREATE UNIQUE INDEX "ClientPerson_clientId_personId_key" ON "ClientPerson"("clientId", "personId");

-- CreateIndex
CREATE INDEX "_PersonToScheduling_B_index" ON "_PersonToScheduling"("B");

-- AddForeignKey
ALTER TABLE "ProfessionGroup" ADD CONSTRAINT "ProfessionGroup_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profession" ADD CONSTRAINT "Profession_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profession" ADD CONSTRAINT "Profession_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "ProfessionGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_professionId_fkey" FOREIGN KEY ("professionId") REFERENCES "Profession"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Client" ADD CONSTRAINT "Client_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Client" ADD CONSTRAINT "Client_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Client" ADD CONSTRAINT "Client_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Person" ADD CONSTRAINT "Person_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Person" ADD CONSTRAINT "Person_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_creatorId_fkey" FOREIGN KEY ("creatorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_recurrenceId_fkey" FOREIGN KEY ("recurrenceId") REFERENCES "Recurrence"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_serviceTypeId_fkey" FOREIGN KEY ("serviceTypeId") REFERENCES "ServiceType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_serviceTypeId_fkey" FOREIGN KEY ("serviceTypeId") REFERENCES "ServiceType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RecurrencePattern" ADD CONSTRAINT "RecurrencePattern_recurrenceId_fkey" FOREIGN KEY ("recurrenceId") REFERENCES "Recurrence"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Location" ADD CONSTRAINT "Location_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Location" ADD CONSTRAINT "Location_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceType" ADD CONSTRAINT "ServiceType_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Insurance" ADD CONSTRAINT "Insurance_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkingHours" ADD CONSTRAINT "WorkingHours_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailConfig" ADD CONSTRAINT "EmailConfig_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionModule" ADD CONSTRAINT "SubscriptionModule_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Branch" ADD CONSTRAINT "Branch_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Contact" ADD CONSTRAINT "Contact_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Contact" ADD CONSTRAINT "Contact_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Contact" ADD CONSTRAINT "Contact_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientInsurance" ADD CONSTRAINT "ClientInsurance_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientInsurance" ADD CONSTRAINT "ClientInsurance_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonInsurance" ADD CONSTRAINT "PersonInsurance_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonInsurance" ADD CONSTRAINT "PersonInsurance_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonInsuranceServiceLimit" ADD CONSTRAINT "PersonInsuranceServiceLimit_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonInsuranceServiceLimit" ADD CONSTRAINT "PersonInsuranceServiceLimit_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonInsuranceServiceLimit" ADD CONSTRAINT "PersonInsuranceServiceLimit_serviceTypeId_fkey" FOREIGN KEY ("serviceTypeId") REFERENCES "ServiceType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ConversationParticipant" ADD CONSTRAINT "ConversationParticipant_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ConversationParticipant" ADD CONSTRAINT "ConversationParticipant_lastReadMessageId_fkey" FOREIGN KEY ("lastReadMessageId") REFERENCES "Message"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ConversationParticipant" ADD CONSTRAINT "ConversationParticipant_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_referencedMessageId_fkey" FOREIGN KEY ("referencedMessageId") REFERENCES "Message"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageStatus" ADD CONSTRAINT "MessageStatus_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "Message"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageStatus" ADD CONSTRAINT "MessageStatus_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "ConversationParticipant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Evaluation" ADD CONSTRAINT "Evaluation_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Evaluation" ADD CONSTRAINT "Evaluation_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Level" ADD CONSTRAINT "Level_evaluationId_fkey" FOREIGN KEY ("evaluationId") REFERENCES "Evaluation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Skill" ADD CONSTRAINT "Skill_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EvaluationSkill" ADD CONSTRAINT "EvaluationSkill_evaluationId_fkey" FOREIGN KEY ("evaluationId") REFERENCES "Evaluation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EvaluationSkill" ADD CONSTRAINT "EvaluationSkill_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "Skill"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Score" ADD CONSTRAINT "Score_evaluationId_fkey" FOREIGN KEY ("evaluationId") REFERENCES "Evaluation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Task" ADD CONSTRAINT "Task_evaluationId_fkey" FOREIGN KEY ("evaluationId") REFERENCES "Evaluation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Task" ADD CONSTRAINT "Task_levelId_fkey" FOREIGN KEY ("levelId") REFERENCES "Level"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Task" ADD CONSTRAINT "Task_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "Skill"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StandardCriteria" ADD CONSTRAINT "StandardCriteria_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Program" ADD CONSTRAINT "Program_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Program" ADD CONSTRAINT "Program_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProgramTarget" ADD CONSTRAINT "ProgramTarget_programId_fkey" FOREIGN KEY ("programId") REFERENCES "Program"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolder" ADD CONSTRAINT "CurriculumFolder_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolder" ADD CONSTRAINT "CurriculumFolder_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolder" ADD CONSTRAINT "CurriculumFolder_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolderProgram" ADD CONSTRAINT "CurriculumFolderProgram_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolderProgram" ADD CONSTRAINT "CurriculumFolderProgram_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolderProgram" ADD CONSTRAINT "CurriculumFolderProgram_curriculumFolderId_fkey" FOREIGN KEY ("curriculumFolderId") REFERENCES "CurriculumFolder"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Anamnese" ADD CONSTRAINT "Anamnese_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Anamnese" ADD CONSTRAINT "Anamnese_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Anamnese" ADD CONSTRAINT "Anamnese_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Anamnese" ADD CONSTRAINT "Anamnese_ultimaAtualizacaoPorId_fkey" FOREIGN KEY ("ultimaAtualizacaoPorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EvolucaoDiaria" ADD CONSTRAINT "EvolucaoDiaria_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EvolucaoDiaria" ADD CONSTRAINT "EvolucaoDiaria_profissionalId_fkey" FOREIGN KEY ("profissionalId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EvolucaoDiaria" ADD CONSTRAINT "EvolucaoDiaria_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EvolucaoDiaria" ADD CONSTRAINT "EvolucaoDiaria_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EvolucaoDiaria" ADD CONSTRAINT "EvolucaoDiaria_ultimaAtualizacaoPorId_fkey" FOREIGN KEY ("ultimaAtualizacaoPorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ArquivoEvolucaoDiaria" ADD CONSTRAINT "ArquivoEvolucaoDiaria_evolucaoDiariaId_fkey" FOREIGN KEY ("evolucaoDiariaId") REFERENCES "EvolucaoDiaria"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ArquivoEvolucaoDiaria" ADD CONSTRAINT "ArquivoEvolucaoDiaria_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ArquivoEvolucaoDiaria" ADD CONSTRAINT "ArquivoEvolucaoDiaria_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientPerson" ADD CONSTRAINT "ClientPerson_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientPerson" ADD CONSTRAINT "ClientPerson_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PersonToScheduling" ADD CONSTRAINT "_PersonToScheduling_A_fkey" FOREIGN KEY ("A") REFERENCES "Person"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_PersonToScheduling" ADD CONSTRAINT "_PersonToScheduling_B_fkey" FOREIGN KEY ("B") REFERENCES "Scheduling"("id") ON DELETE CASCADE ON UPDATE CASCADE;
