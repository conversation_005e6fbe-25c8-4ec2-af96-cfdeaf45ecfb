'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Settings, Users, Calendar, DollarSign, LayoutDashboard, LogOut, Menu, X,
  Calculator, FileText, Building, Clock, Lock, Database, CreditCard,
  BarChart, TrendingUp, Gift, GraduationCap, Home, Box, UserPlus, UserCheck,
  MapPin, Tag, Bell, ChevronDown, ShieldCheck, Shield, UserCog,
  MessageCircle, Search, ChevronRight, ShieldIcon, Info,
  Briefcase, Construction, HardHat, Brain, Activity, BookOpen, ClipboardList, Award
} from 'lucide-react';
import { useQuickNav } from '@/contexts/QuickNavContext';
import { useConstructionMessage } from '@/hooks/useConstructionMessage';
import { ConstructionButton } from '@/components/construction';
import { ChatButton } from '@/components/chat';
import { useRouter, usePathname } from 'next/navigation';
import { ThemeToggle } from '@/components/ThemeToggle';
import { APP_VERSION } from '@/config/appConfig';

// Header Component
const Header = ({ toggleSidebar, isSidebarOpen }) => {
  const { user, logout } = useAuth();
  const router = useRouter();
  const { openQuickNav } = useQuickNav();

  // Função para determinar o ícone e as cores do papel do usuário
  const getRoleInfo = () => {
    switch (user?.role) {
      case 'SYSTEM_ADMIN':
        return {
          icon: ShieldCheck,
          bgColor: 'bg-red-50 dark:bg-red-900',
          textColor: 'text-red-700 dark:text-red-300',
          name: 'Admin do Sistema'
        };
      case 'COMPANY_ADMIN':
        return {
          icon: Shield,
          bgColor: 'bg-blue-50 dark:bg-blue-900',
          textColor: 'text-blue-700 dark:text-blue-300',
          name: 'Admin da Empresa'
        };
      default:
        return {
          icon: UserCog,
          bgColor: 'bg-green-50 dark:bg-green-900',
          textColor: 'text-green-700 dark:text-green-300',
          name: 'Funcionário'
        };
    }
  };

  const roleInfo = getRoleInfo();
  const RoleIcon = roleInfo.icon;

  // Pegar primeira letra de cada nome para o avatar
  const getInitials = () => {
    if (!user?.fullName) return 'U';

    const names = user.fullName.split(' ');
    if (names.length === 1) return names[0].charAt(0);

    return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
  };

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9000]">
      {/* Lado esquerdo: Logo e Toggle */}
      <div className="flex items-center gap-3">
        <button
          onClick={toggleSidebar}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors"
          aria-label={isSidebarOpen ? "Fechar menu lateral" : "Abrir menu lateral"}
        >
          {isSidebarOpen ? <X size={22} aria-hidden="true" /> : <Menu size={22} aria-hidden="true" />}
        </button>

        <div className="flex items-center">
          <div className="relative">
            <img
              src="/logo_horizontal_sem_fundo.png"
              alt="High Tide Logo"
              className="h-10 mr-2.5 dark:invert dark:text-white"
            />
            <span className="absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono">{APP_VERSION}</span>
          </div>
        </div>
      </div>

      {/* Lado direito: Pesquisa, Notificações e Perfil */}
      <div className="flex items-center gap-3">
        {/* Botão de pesquisa rápida */}
        <button
          onClick={openQuickNav}
          className="flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors"
          aria-label="Abrir pesquisa rápida"
        >
          <Search size={18} className="text-gray-400 dark:text-gray-500" aria-hidden="true" />
          <span className="hidden sm:inline">Pesquisar...</span>
          <div className="hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400">
            <span>Ctrl + K</span>
          </div>
        </button>

        {/* Botões de notificação */}
        <ChatButton />

        <ConstructionButton
          className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full relative transition-colors"
          aria-label="Notificações"
          title="Sistema de Notificações"
          content="O sistema de notificações está em desenvolvimento e estará disponível em breve. Você receberá alertas sobre eventos importantes no sistema."
          icon="Bell"
        >
          <Bell size={20} aria-hidden="true" />
          <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-primary-500" aria-hidden="true"></span>
        </ConstructionButton>

        {/* Botão de configurações */}
        <button
          onClick={() => router.push('/dashboard/admin/settings')}
          className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          aria-label="Configurações"
        >
          <Settings size={20} aria-hidden="true" />
        </button>

        {/* Theme Toggle Button */}
        <ThemeToggle />

        {/* Divisor vertical */}
        <div className="h-8 border-l border-gray-200 dark:border-gray-700 mx-1" aria-hidden="true"></div>

        {/* Dropdown de usuário */}
        <div className="relative group">
          <button
            className="flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            aria-expanded="false"
            aria-haspopup="true"
            aria-label="Menu do usuário"
          >
            <div className="h-9 w-9 rounded-full flex items-center justify-center font-medium overflow-hidden">
              {user?.profileImageFullUrl ? (
                <img
                  src={user.profileImageFullUrl}
                  alt={`Foto de perfil de ${user?.fullName || 'Usuário'}`}
                  className="h-10 w-10 rounded-full object-cover"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.style.display = 'none';
                    e.target.parentNode.innerHTML = getInitials();
                  }}
                />
              ) : (
                getInitials()
              )}
            </div>

            <div className="hidden md:block text-left">
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1">{user?.fullName || 'Usuário'}</p>
              <div className={`text-xs ${roleInfo.textColor} px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 ${roleInfo.bgColor}`}>
                <RoleIcon size={10} className="mr-1" aria-hidden="true" />
                <span>{roleInfo.name}</span>
              </div>
            </div>

            <ChevronDown size={16} className="text-gray-400 dark:text-gray-500 hidden md:block" aria-hidden="true" />
          </button>

          {/* Menu dropdown */}
          <div className="absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right"
               role="menu"
               aria-orientation="vertical"
               aria-labelledby="user-menu-button">
            <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden">
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200">{user?.fullName || 'Usuário'}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{user?.email || '<EMAIL>'}</p>
            </div>

            <div className="px-4 py-2">
              <p className="text-xs text-gray-500 dark:text-gray-400">Empresa</p>
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200">{user?.company?.name || 'Minha Empresa'}</p>
            </div>

            <div className="border-t border-gray-100 dark:border-gray-700 pt-1 mt-1">
              <button
                onClick={() => router.push('/dashboard/profile')}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                role="menuitem"
              >
                Meu Perfil
              </button>
              <button
                onClick={logout}
                className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors"
                role="menuitem"
              >
                <LogOut size={14} className="mr-2" aria-hidden="true" />
                Sair do Sistema
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

// Navigation Link Component - Atualizado para usar o sistema de temas por módulo
const NavLink = ({ icon: Icon, title, href, isAccessible, moduleId = 'scheduler' }) => {
  const router = useRouter();
  const pathname = usePathname();

  // Verifica se o link está ativo comparando com o pathname atual
  const isActive = pathname === href;

  return (
    <button
      onClick={() => router.push(href)}
      disabled={!isAccessible}
      className={`
        w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors
        ${isActive
          ? `bg-module-${moduleId}-bg text-module-${moduleId}-icon`
          : isAccessible
            ? 'text-gray-600 hover:bg-gray-100'
            : 'opacity-50 cursor-not-allowed'
        }
      `}
      aria-current={isActive ? 'page' : undefined}
      role="link"
      aria-disabled={!isAccessible}
    >
      <Icon size={20} aria-hidden="true" />
      <span className="font-medium">{title}</span>
    </button>
  );
};

// Configuração dos módulos principais - mantida igual
export const modules = [
  {
    id: 'admin',
    title: 'Administração',
    icon: ShieldIcon,
    description: 'Gerencie todo o sistema, incluindo usuários e configurações.',
    role: 'ADMIN'
  },
  // {
  //   id: 'financial',
  //   title: 'Financeiro',
  //   icon: DollarSign,
  //   description: 'Controle receitas, despesas e gere relatórios financeiros detalhados.',
  //   role: 'FINANCIAL'
  // },
  // {
  //   id: 'hr',
  //   title: 'RH',
  //   icon: Users,
  //   description: 'Gerencie informações de funcionários, admissões e folha de pagamento.',
  //   role: 'RH'
  // },
  {
    id: 'people',
    title: 'Pessoas',
    icon: UserCheck,
    description: 'Cadastre e gerencie informações de pacientes e clientes.',
    role: 'BASIC'
  },
  {
    id: 'scheduler',
    title: 'Agendamento',
    icon: Calendar,
    description: 'Agende e gerencie compromissos, consultas e eventos.',
    role: 'BASIC'
  },
  {
    id: 'abaplus',
    title: 'ABA+',
    icon: Brain,
    description: 'Gerencie programas ABA, acompanhamento terapêutico.',
    role: 'BASIC'
  }
];

// Submódulos para cada módulo principal - apenas ABA+ com grupos
export const moduleSubmenus = {
  admin: [
    { id: 'introduction', title: 'Introdução', icon: Info, description: 'Visão geral do módulo de administração' },
    { id: 'users', title: 'Usuários', icon: Users, description: 'Gerenciar usuários do sistema' },
    { id: 'professions', title: 'Profissões', icon: Briefcase, description: 'Gerenciar profissões e grupos' },
    { id: 'plans', title: 'Planos', icon: CreditCard, description: 'Gerenciar plano e assinatura' },
    { id: 'settings', title: 'Configurações', icon: Settings, description: 'Configurações gerais do sistema' },
    { id: 'logs', title: 'Logs', icon: FileText, description: 'Histórico de atividades do sistema' },
    { id: 'backup', title: 'Backup', icon: Database, description: 'Gerenciamento de backup dos dados' },
    { id: 'dashboard', title: 'Dashboard', icon: LayoutDashboard, description: 'Visão geral do sistema' }
  ],
  financial: [
    { id: 'invoices', title: 'Faturas', icon: FileText, description: 'Gerenciar faturas e cobranças' },
    { id: 'payments', title: 'Pagamentos', icon: CreditCard, description: 'Controle de pagamentos' },
    { id: 'expenses', title: 'Despesas', icon: DollarSign, description: 'Gestão de despesas' },
    { id: 'reports', title: 'Relatórios', icon: BarChart, description: 'Relatórios financeiros' },
    { id: 'cashflow', title: 'Fluxo de Caixa', icon: TrendingUp, description: 'Análise de fluxo de caixa' }
  ],
  hr: [
    { id: 'employees', title: 'Funcionários', icon: Users, description: 'Gerenciar funcionários' },
    { id: 'payroll', title: 'Folha de Pagamento', icon: Calculator, description: 'Processamento de salários' },
    { id: 'documents', title: 'Documentos', icon: FileText, description: 'Documentos e formulários de RH' },
    { id: 'departments', title: 'Departamentos', icon: Building, description: 'Gestão de departamentos' },
    { id: 'attendance', title: 'Ponto', icon: Clock, description: 'Controle de ponto e ausências' },
    { id: 'benefits', title: 'Benefícios', icon: Gift, description: 'Gestão de benefícios' },
    { id: 'training', title: 'Treinamentos', icon: GraduationCap, description: 'Gestão de treinamentos' }
  ],
  people: [
    { id: 'introduction', title: 'Introdução', icon: Info, description: 'Visão geral do módulo de pessoas' },
    { id: 'clients', title: 'Clientes', icon: UserPlus, description: 'Gerenciar clientes e contas' },
    { id: 'persons', title: 'Pacientes', icon: Users, description: 'Gerenciar cadastro de pacientes' },
    { id: 'insurances', title: 'Convênios', icon: CreditCard, description: 'Gerenciar convênios associados' },
    { id: 'insurance-limits', title: 'Limites de Convênio', icon: Shield, description: 'Gerenciar limites de serviço por convênio' },
    { id: 'dashboard', title: 'Dashboard', icon: LayoutDashboard, description: 'Análise e estatísticas de pessoas' }
  ],
  scheduler: [
    { id: 'introduction', title: 'Introdução', icon: Info, description: 'Visão geral do módulo de agendamento' },
    { id: 'calendar', title: 'Agendar Consulta', icon: Calendar, description: 'Visualizar agenda completa' },
    { id: 'working-hours', title: 'Horários de Trabalho', icon: Clock, description: 'Configurar horários de trabalho' },
    { id: 'service-types', title: 'Tipos de Serviço', icon: Tag, description: 'Gerenciar tipos de serviço' },
    { id: 'locations', title: 'Localizações', icon: MapPin, description: 'Gerenciar localizações e endereços' },
    { id: 'occupancy', title: 'Ocupação', icon: Briefcase, description: 'Análise detalhada da ocupação dos profissionais' },
    { id: 'appointments-report', title: 'Relatório', icon: FileText, description: 'Gerenciar agendamentos em formato de lista' },
    { id: 'appointments-dashboard', title: 'Dashboard', icon: LayoutDashboard, description: 'Análise de agendamentos e estatísticas' }
  ],
  abaplus: [
    { id: 'introduction', title: 'Introdução', icon: Info, description: 'Visão geral do módulo ABA+' },
    { id: 'dashboard', title: 'Dashboard', icon: LayoutDashboard, description: 'Análise e estatísticas ABA+' },
    {
      id: 'cadastro',
      title: 'Cadastro',
      type: 'group',
      items: [
        { id: 'skills', title: 'Habilidades', icon: Activity, description: 'Gerenciar habilidades e competências' },
        { id: 'programs', title: 'Programas', icon: BookOpen, description: 'Gerenciar programas terapêuticos' },
        { id: 'evaluations', title: 'Avaliações', icon: ClipboardList, description: 'Gerenciar avaliações e protocolos' },
        { id: 'standard-criteria', title: 'Critérios Padrão', icon: Award, description: 'Gerenciar critérios padrão' },
        { id: 'curriculum-folders', title: 'Pastas Curriculares', icon: FileText, description: 'Gerenciar pastas curriculares dos aprendizes' },
      ]
    },
    {
      id: 'atendimento',
      title: 'Atendimento',
      type: 'group',
      items: [
        { id: 'anamnese', title: 'Anamnese', icon: ClipboardList, description: 'Gerenciar anamneses dos pacientes' },
        { id: 'evolucoes-diarias', title: 'Evoluções Diárias', icon: FileText, description: 'Gerenciar evoluções diárias dos atendimentos' },
        { id: 'sessao', title: 'Sessão', icon: Calendar, description: 'Gerenciar sessões de atendimento' },
      ]
    }
  ]
};

export { Header, NavLink };