// src/middlewares/requestLogger.js
const { v4: uuidv4 } = require('uuid');
const loggerService = require('../services/loggerService');

/**
 * Middleware para logging estruturado de requests
 * Adiciona ID único para cada request e registra informações detalhadas
 */
const requestLoggerMiddleware = (req, res, next) => {
  // Gerar ID único para o request
  const requestId = uuidv4();
  req.requestId = requestId;

  // Capturar informações do request
  const startTime = Date.now();
  const { method, originalUrl, ip, headers } = req;
  const userAgent = headers['user-agent'] || 'Unknown';

  // Criar logger contextual para este request
  const requestLogger = loggerService.child({
    requestId,
    ip,
    userAgent,
    method,
    url: originalUrl
  });

  // Adicionar logger ao request para uso em outros middlewares/controllers
  req.logger = requestLogger;

  // Log inicial do request
  requestLogger.http('REQUEST_START', {
    headers: {
      'content-type': headers['content-type'],
      'authorization': headers.authorization ? '[REDACTED]' : undefined,
      'x-forwarded-for': headers['x-forwarded-for']
    },
    query: req.query,
    // Não logar o body por questões de segurança e performance
    bodySize: req.headers['content-length'] || 0
  });

  // Interceptar o final da resposta
  const originalSend = res.send;
  const originalJson = res.json;

  res.send = function(data) {
    logResponse(data);
    return originalSend.call(this, data);
  };

  res.json = function(data) {
    logResponse(data);
    return originalJson.call(this, data);
  };

  function logResponse(responseData) {
    const duration = Date.now() - startTime;
    const { statusCode } = res;

    // Determinar nível de log baseado no status code
    let logLevel = 'http';
    if (statusCode >= 500) {
      logLevel = 'error';
    } else if (statusCode >= 400) {
      logLevel = 'warn';
    }

    // Preparar dados da resposta (sem conteúdo sensível)
    const responseInfo = {
      statusCode,
      duration,
      responseSize: JSON.stringify(responseData || '').length
    };

    // Adicionar informações do usuário se disponível
    if (req.user) {
      responseInfo.userId = req.user.id;
      responseInfo.companyId = req.user.companyId;
      responseInfo.userRole = req.user.role;
    }

    // Log da resposta
    requestLogger[logLevel]('REQUEST_COMPLETE', responseInfo);

    // Log de performance para requests lentos
    if (duration > 1000) { // Mais de 1 segundo
      requestLogger.performance('SLOW_REQUEST', duration, {
        threshold: 1000,
        statusCode,
        url: originalUrl,
        method
      });
    }

    // Log de auditoria para operações importantes
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method) && statusCode < 400) {
      const action = getActionFromRequest(method, originalUrl);
      if (action) {
        requestLogger.audit(action.type, action.entity, action.entityId || 'unknown', {
          method,
          url: originalUrl,
          statusCode
        });
      }
    }
  }

  next();
};

/**
 * Determinar o tipo de ação baseado no método e URL
 */
function getActionFromRequest(method, url) {
  const urlParts = url.split('/').filter(part => part && part !== 'api');
  
  if (urlParts.length === 0) return null;

  const entity = urlParts[0].toUpperCase();
  const entityId = urlParts[1];

  let actionType;
  switch (method) {
    case 'POST':
      actionType = 'CREATE';
      break;
    case 'PUT':
    case 'PATCH':
      actionType = 'UPDATE';
      break;
    case 'DELETE':
      actionType = 'DELETE';
      break;
    default:
      return null;
  }

  return {
    type: actionType,
    entity,
    entityId
  };
}

/**
 * Middleware para capturar erros não tratados
 */
const errorLoggerMiddleware = (err, req, res, next) => {
  const requestLogger = req.logger || loggerService;

  // Log do erro
  requestLogger.error('UNHANDLED_ERROR', {
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name
    },
    statusCode: err.statusCode || 500
  });

  // Log de segurança para certos tipos de erro
  if (err.name === 'ValidationError' || err.statusCode === 401 || err.statusCode === 403) {
    requestLogger.security('SECURITY_ERROR', 'medium', {
      errorType: err.name,
      statusCode: err.statusCode,
      message: err.message
    });
  }

  next(err);
};

/**
 * Middleware para logging de operações de database
 */
const databaseLoggerMiddleware = (operation, table) => {
  return (req, res, next) => {
    const startTime = Date.now();
    const requestLogger = req.logger || loggerService;

    // Interceptar a resposta para medir duração
    const originalSend = res.send;
    const originalJson = res.json;

    res.send = function(data) {
      logDatabaseOperation();
      return originalSend.call(this, data);
    };

    res.json = function(data) {
      logDatabaseOperation();
      return originalJson.call(this, data);
    };

    function logDatabaseOperation() {
      const duration = Date.now() - startTime;
      
      requestLogger.database(operation, table, duration, {
        success: res.statusCode < 400
      });

      // Log de performance para operações lentas de database
      if (duration > 500) { // Mais de 500ms
        requestLogger.performance('SLOW_DATABASE_OPERATION', duration, {
          operation,
          table,
          threshold: 500
        });
      }
    }

    next();
  };
};

/**
 * Middleware para logging de operações de cache
 */
const cacheLoggerMiddleware = (operation, key) => {
  return (req, res, next) => {
    const startTime = Date.now();
    const requestLogger = req.logger || loggerService;

    // Adicionar função de log de cache ao request
    req.logCache = (hit = false) => {
      const duration = Date.now() - startTime;
      requestLogger.cache(operation, key, hit, duration);
    };

    next();
  };
};

module.exports = {
  requestLoggerMiddleware,
  errorLoggerMiddleware,
  databaseLoggerMiddleware,
  cacheLoggerMiddleware
};
