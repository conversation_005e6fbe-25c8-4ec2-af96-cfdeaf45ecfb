"use client";

import React, { useState, useEffect, useRef } from "react";
import { X, Loader2, Building, MapPin, Phone, Mail, Check, Star, Clock } from "lucide-react";
import { InputMask } from "@react-input/mask";
import { useAuth } from "@/contexts/AuthContext";
import { branchService } from "@/app/modules/admin/services/branchService";
import { companyService } from "@/app/modules/admin/services/companyService";
import AddressForm from "@/components/common/AddressForm";
import { ModuleInput, ModuleSelect, ModuleFormGroup } from "@/components/ui";
import BranchWorkingHoursForm from "@/components/workingHours/BranchWorkingHoursForm";
import { useToast } from "@/contexts/ToastContext";
import MaskedInput from "@/components/common/MaskedInput";


const BranchFormModal = ({ isOpen, onClose, branch, onSuccess }) => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  const workingHoursFormRef = useRef(null);
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    address: "",
    neighborhood: "",
    city: "",
    state: "",
    postalCode: "",
    phone: "",
    email: "",
    isHeadquarters: false,
    companyId: "",
    defaultWorkingHours: null,
    applyToUsers: false
  });

  const [companies, setCompanies] = useState([]);
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [loadingCompanies, setLoadingCompanies] = useState(false);
  const [activeTab, setActiveTab] = useState("basic"); // basic, workingHours

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  useEffect(() => {
    // When editing an existing branch, fill the form
    if (branch) {
      setFormData({
        name: branch.name || "",
        code: branch.code || "",
        description: branch.description || "",
        address: branch.address || "",
        neighborhood: branch.neighborhood || "",
        city: branch.city || "",
        state: branch.state || "",
        postalCode: branch.postalCode || "",
        phone: branch.phone || "",
        email: branch.email || "",
        isHeadquarters: branch.isHeadquarters || false,
        companyId: branch.companyId || user?.companyId || "",
        defaultWorkingHours: branch.defaultWorkingHours || null,
        applyToUsers: false
      });

      // If branch exists but doesn't have default working hours, load them from API
      if (branch.id && !branch.defaultWorkingHours) {
        loadDefaultWorkingHours(branch.id);
      }
    } else {
      // New branch
      resetForm();
    }

    // For system admins, load company options
    if (isSystemAdmin) {
      loadCompanies();
    }
  }, [branch, isOpen, user]);

  const loadCompanies = async () => {
    setLoadingCompanies(true);
    try {
      const response = await companyService.getCompanies({
        active: true,
        limit: 100
      });

      setCompanies(response.companies || []);
    } catch (error) {
      console.error("Error loading companies:", error);
    } finally {
      setLoadingCompanies(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      code: "",
      description: "",
      address: "",
      city: "",
      state: "",
      postalCode: "",
      phone: "",
      email: "",
      isHeadquarters: false,
      companyId: user?.companyId || "", // Set to user's company by default
      defaultWorkingHours: null,
      applyToUsers: false
    });
    setErrors({});
    setActiveTab("basic");
  };

  const loadDefaultWorkingHours = async (branchId) => {
    try {
      const data = await branchService.getDefaultWorkingHours(branchId);
      setFormData(prev => ({
        ...prev,
        defaultWorkingHours: data.defaultWorkingHours
      }));
    } catch (error) {
      console.error("Error loading default working hours:", error);
      // Don't set an error, just use the default working hours
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name) newErrors.name = "Nome da unidade é obrigatório";
    if (!formData.address) newErrors.address = "Endereço é obrigatório";
    if (!formData.companyId) newErrors.companyId = "Empresa é obrigatória";

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }

    if (formData.phone && !/^\d{10,11}$/.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = "Telefone inválido";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value, checked, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear errors when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleWorkingHoursChange = (workingHours) => {
    console.log('BranchFormModal handleWorkingHoursChange - recebendo dados:', workingHours);

    // Garante que não estamos sobrescrevendo o estado anterior com valores vazios
    if (workingHours) {
      setFormData(prev => {
        const newFormData = {
          ...prev,
          defaultWorkingHours: workingHours
        };
        console.log('BranchFormModal handleWorkingHoursChange - novo estado:', newFormData.defaultWorkingHours);
        return newFormData;
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validar o formulário básico
    if (!validateForm()) return;

    // Se estiver na aba de horários de trabalho, validar os horários
    if (activeTab === "workingHours" && workingHoursFormRef.current) {
      const isWorkingHoursValid = workingHoursFormRef.current.validateAllTimeSlots();
      if (!isWorkingHoursValid) {
        toast_error({
          title: "Erro de validação",
          message: "Verifique os horários de trabalho e corrija os erros antes de salvar."
        });
        return;
      }
    }

    setIsLoading(true);

    try {
      const payload = {
        name: formData.name,
        code: formData.code || undefined,
        description: formData.description || undefined,
        address: formData.address,
        neighborhood: formData.neighborhood || undefined,
        city: formData.city || undefined,
        state: formData.state || undefined,
        postalCode: formData.postalCode || undefined,
        phone: formData.phone ? formData.phone.replace(/\D/g, '') : undefined,
        email: formData.email || undefined,
        isHeadquarters: formData.isHeadquarters,
        companyId: formData.companyId,
        defaultWorkingHours: formData.defaultWorkingHours,
        applyToUsers: formData.applyToUsers
      };

      let result;
      if (branch) {
        // Update existing branch
        result = await branchService.updateBranch(branch.id, payload);

        // If applyToUsers is true, apply working hours to all users
        if (formData.applyToUsers && formData.defaultWorkingHours) {
          try {
            await branchService.applyWorkingHoursToUsers(branch.id);
            toast_success({
              title: "Horários aplicados",
              message: "Horários de trabalho aplicados com sucesso aos usuários da unidade"
            });
          } catch (error) {
            console.error("Error applying working hours to users:", error);
            toast_error({
              title: "Erro",
              message: "Erro ao aplicar horários de trabalho aos usuários"
            });
          }
        }
      } else {
        // Create new branch
        result = await branchService.createBranch(payload);
      }

      if (onSuccess) onSuccess();
    } catch (error) {
      console.error("Error saving branch:", error);

      if (error.response?.data?.errors) {
        const apiErrors = {};
        error.response.data.errors.forEach(err => {
          apiErrors[err.param] = err.msg;
        });
        setErrors(apiErrors);
      } else {
        setErrors({
          submit: error.response?.data?.message || "Erro ao salvar unidade"
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // CSS classes with dark mode
  const inputClasses = "block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-neutral-900 dark:text-gray-100";
  const labelClasses = "block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1";
  const errorClasses = "mt-1 text-xs text-red-600 dark:text-red-400";

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Background overlay */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-3xl w-full max-h-[90vh] flex flex-col z-[11050]">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-white flex items-center gap-2">
            <Building className="h-5 w-5 text-primary-500 dark:text-primary-400" />
            {branch ? "Editar Unidade" : "Nova Unidade"}
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-neutral-200 dark:border-gray-700">
          <button
            onClick={() => setActiveTab("basic")}
            className={`px-6 py-3 font-medium text-sm transition-colors ${activeTab === "basic"
              ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-500 dark:border-primary-400'
              : 'text-neutral-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400'}`}
          >
            <div className="flex items-center gap-2">
              <Building size={16} />
              Informações Básicas
            </div>
          </button>
          <button
            onClick={() => setActiveTab("workingHours")}
            className={`px-6 py-3 font-medium text-sm transition-colors ${activeTab === "workingHours"
              ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-500 dark:border-primary-400'
              : 'text-neutral-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400'}`}
          >
            <div className="flex items-center gap-2">
              <Clock size={16} />
              Horários de Trabalho
            </div>
          </button>
        </div>

        {/* Form */}
        <div className="overflow-y-auto p-6">
          <form onSubmit={handleSubmit}>
            {errors.submit && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg">
                {errors.submit}
              </div>
            )}

            {activeTab === "basic" && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Info */}
                <div className="md:col-span-2">
                  <h4 className="text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                    <Building className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                    Informações Básicas
                  </h4>
                </div>

              {/* Company selection for system admins */}
              {isSystemAdmin && (
                <div className="md:col-span-2">
                  <ModuleFormGroup
                    moduleColor="admin"
                    label="Empresa *"
                    htmlFor="companyId"
                    icon={<Building size={16} />}
                    error={!!errors.companyId}
                    errorMessage={errors.companyId}
                  >
                    <ModuleSelect
                      moduleColor="admin"
                      id="companyId"
                      name="companyId"
                      value={formData.companyId}
                      onChange={handleChange}
                      disabled={isLoading || loadingCompanies}
                      required
                      placeholder="Selecione uma empresa"
                      error={!!errors.companyId}
                    >
                      <option value="">Selecione uma empresa</option>
                      {loadingCompanies ? (
                        <option value="" disabled>Carregando empresas...</option>
                      ) : (
                        companies.map(company => (
                          <option key={company.id} value={company.id}>
                            {company.name}
                          </option>
                        ))
                      )}
                    </ModuleSelect>
                  </ModuleFormGroup>
                </div>
              )}

              {/* Name */}
              <div>
                <ModuleFormGroup
                  moduleColor="admin"
                  label="Nome da Unidade *"
                  htmlFor="name"
                  icon={<Building size={16} />}
                  error={!!errors.name}
                  errorMessage={errors.name}
                >
                  <ModuleInput
                    moduleColor="admin"
                    id="name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Nome da unidade"
                    disabled={isLoading}
                    required
                    error={!!errors.name}
                  />
                </ModuleFormGroup>
              </div>

              {/* Code */}
              <div>
                <ModuleFormGroup
                  moduleColor="admin"
                  label="Código"
                  htmlFor="code"
                  icon={<Building size={16} />}
                >
                  <ModuleInput
                    moduleColor="admin"
                    id="code"
                    name="code"
                    type="text"
                    value={formData.code}
                    onChange={handleChange}
                    placeholder="Código único (opcional)"
                    disabled={isLoading}
                  />
                  <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                    Código único para identificação da unidade
                  </p>
                </ModuleFormGroup>
              </div>

              {/* Description */}
              <div className="md:col-span-2">
                <ModuleFormGroup
                  moduleColor="admin"
                  label="Descrição"
                  htmlFor="description"
                  icon={<Building size={16} />}
                >
                  <ModuleInput
                    moduleColor="admin"
                    id="description"
                    name="description"
                    type="textarea"
                    value={formData.description}
                    onChange={handleChange}
                    rows="3"
                    placeholder="Breve descrição da unidade"
                    disabled={isLoading}
                  />
                </ModuleFormGroup>
              </div>

              {/* Address Section */}
              <div className="md:col-span-2">
                <h4 className="text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                  Endereço
                </h4>
              </div>

              {/* Address Form Component */}
              <div className="md:col-span-2">
                <AddressForm
                  formData={formData}
                  setFormData={setFormData}
                  errors={errors}
                  isLoading={isLoading}
                  fieldMapping={{
                    // Mapeamento personalizado para os campos da API ViaCEP
                    logradouro: "address",
                    bairro: "neighborhood",
                    localidade: "city",
                    uf: "state",
                    cep: "postalCode"
                  }}
                  classes={{
                    label: labelClasses,
                    input: inputClasses,
                    error: errorClasses
                  }}
                />
              </div>

              {/* Contact Section */}
              <div className="md:col-span-2">
                <h4 className="text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                  <Phone className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                  Contato
                </h4>
              </div>

              {/* Phone */}
              <div>
                <label className={labelClasses} htmlFor="phone">
                  Telefone
                </label>
                <MaskedInput
                  type="phone"
                  value={formData.phone}
                  onChange={(e) => handleChange({
                    target: { name: "phone", value: e.target.value }
                  })}
                  placeholder="(00) 00000-0000"
                  className={`${inputClasses} ${errors.phone ? "border-red-500 dark:border-red-700" : ""}`}
                  disabled={isLoading}
                />
                {errors.phone && <p className={errorClasses}>{errors.phone}</p>}
              </div>

              {/* Email */}
              <div>
                <label className={labelClasses} htmlFor="email">
                  Email
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`${inputClasses} ${errors.email ? "border-red-500 dark:border-red-700" : ""}`}
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                />
                {errors.email && <p className={errorClasses}>{errors.email}</p>}
              </div>

              {/* Headquarters checkbox */}
              <div className="md:col-span-2">
                <div className="flex items-center gap-2">
                  <input
                    id="isHeadquarters"
                    name="isHeadquarters"
                    type="checkbox"
                    checked={formData.isHeadquarters}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-400 border-neutral-300 dark:border-gray-600 rounded"
                    disabled={isLoading || (branch && branch.isHeadquarters)}
                  />
                  <label htmlFor="isHeadquarters" className="flex items-center gap-1 text-sm text-neutral-800 dark:text-gray-200">
                    <Star className="h-4 w-4 text-amber-500 dark:text-amber-400" />
                    Definir como matriz/sede principal
                  </label>
                </div>
                <p className="ml-6 mt-1 text-xs text-neutral-500 dark:text-gray-400">
                  Apenas uma unidade pode ser definida como matriz por empresa. Isso alterará automaticamente o status de outras unidades.
                </p>
              </div>
            </div>
            )}

            {activeTab === "workingHours" && (
              <div>
                <BranchWorkingHoursForm
                  ref={workingHoursFormRef}
                  defaultWorkingHours={formData.defaultWorkingHours}
                  onChange={handleWorkingHoursChange}
                  isLoading={isLoading}
                  labelClasses={labelClasses}
                  inputClasses={inputClasses}
                  errorClasses={errorClasses}
                  onValidationChange={(isValid) => {
                    // Opcional: Podemos usar isso para atualizar o estado do botão de salvar
                    // ou mostrar um indicador visual de validação
                  }}
                />

                {branch && (
                  <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-2">
                      <input
                        id="applyToUsers"
                        name="applyToUsers"
                        type="checkbox"
                        checked={formData.applyToUsers}
                        onChange={handleChange}
                        className="h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-400 border-neutral-300 dark:border-gray-600 rounded"
                        disabled={isLoading}
                      />
                      <label htmlFor="applyToUsers" className="text-sm text-neutral-800 dark:text-gray-200">
                        Aplicar horários a todos os usuários desta unidade
                      </label>
                    </div>
                    <p className="ml-6 mt-1 text-xs text-neutral-500 dark:text-gray-400">
                      Marque esta opção para aplicar os horários de trabalho a todos os usuários já associados a esta unidade
                    </p>
                  </div>
                )}
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 size={16} className="animate-spin" />
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Check size={16} />
                <span>Salvar</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BranchFormModal;