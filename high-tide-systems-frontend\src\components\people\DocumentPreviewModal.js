"use client";

import React, { useState, useEffect } from "react";
import { X, Download, Loader2, FileText, Image, Eye } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

const DocumentPreviewModal = ({ isOpen, onClose, document }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  
  useEffect(() => {
    if (isOpen && document) {
      setIsLoading(true);
      setError(null);
      loadPreview();
    }
    
    return () => {
      // Cleanup preview URL when modal closes
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [isOpen, document]);
  
  const loadPreview = async () => {
    try {
      // Get the document content from API
      const response = await fetch(`/api/documents/${document.id}`);
      
      if (!response.ok) {
        throw new Error(`Erro ao carregar documento: ${response.statusText}`);
      }
      
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      setPreviewUrl(url);
    } catch (err) {
      console.error("Erro ao carregar preview:", err);
      setError(err.message || "Não foi possível carregar a pré-visualização");
    } finally {
      setIsLoading(false);
    }
  };
  
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    
    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };
  
  const getDocumentTypeDisplay = (type) => {
    const typeMap = {
      "RG": "RG",
      "CPF": "CPF",
      "CNH": "Carteira de Motorista",
      "COMP_RESIDENCIA": "Comprovante de Residência",
      "CERTIDAO_NASCIMENTO": "Certidão de Nascimento",
      "CERTIDAO_CASAMENTO": "Certidão de Casamento",
      "CARTAO_VACINACAO": "Cartão de Vacinação",
      "PASSAPORTE": "Passaporte",
      "TITULO_ELEITOR": "Título de Eleitor",
      "CARTEIRA_TRABALHO": "Carteira de Trabalho",
      "OUTROS": "Outros"
    };
    
    return typeMap[type] || type;
  };
  
  const isImage = (mimeType) => {
    return mimeType && mimeType.startsWith('image/');
  };
  
  const isPDF = (mimeType) => {
    return mimeType === 'application/pdf';
  };
  
  if (!isOpen || !document) return null;
  
  const documentMimeType = document.mimeType || '';
  
  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-4xl w-full max-h-[90vh] flex flex-col z-[11050]">
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-gray-100">
              {document.filename}
            </h3>
            <span className="px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-800 dark:text-gray-300">
              {getDocumentTypeDisplay(document.type)}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => window.open(`/api/documents/${document.id}?download=true`, '_blank')}
              className="p-2 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Baixar"
            >
              <Download size={20} />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-200 hover:bg-neutral-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Fechar"
            >
              <X size={20} />
            </button>
          </div>
        </div>
        
        <div className="flex-1 overflow-hidden p-4">
          <div className="bg-neutral-50 dark:bg-gray-900 rounded-lg h-full flex flex-col">
            {isLoading ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="flex flex-col items-center gap-2">
                  <Loader2 size={32} className="text-primary-500 dark:text-primary-400 animate-spin" />
                  <p className="text-neutral-600 dark:text-gray-300">Carregando documento...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 max-w-md">
                  <p className="font-medium mb-2">Erro ao carregar documento</p>
                  <p>{error}</p>
                </div>
              </div>
            ) : (
              <div className="flex-1 overflow-auto">
                {isPDF(documentMimeType) ? (
                  <div className="h-full w-full">
                    <iframe 
                      src={`${previewUrl}#view=FitH`} 
                      className="w-full h-full" 
                      title={document.filename}
                    />
                  </div>
                ) : isImage(documentMimeType) ? (
                  <div className="flex items-center justify-center h-full">
                    <img 
                      src={previewUrl} 
                      alt={document.filename}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                ) : (
                  <div className="flex-1 flex items-center justify-center p-4">
                    <div className="flex flex-col items-center gap-4 text-neutral-600 dark:text-gray-300 max-w-md text-center">
                      <FileText size={64} className="text-neutral-400 dark:text-gray-500" />
                      <p>
                        Este tipo de documento não pode ser pré-visualizado diretamente.
                        Clique em "Baixar" para visualizar o arquivo.
                      </p>
                      <button
                        onClick={() => window.open(`/api/documents/${document.id}`, '_blank')}
                        className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
                      >
                        <Eye size={16} />
                        <span>Abrir em nova aba</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        
        <div className="px-6 py-4 border-t border-neutral-200 dark:border-gray-700 text-sm text-neutral-500 dark:text-gray-400">
          <p>Adicionado em {formatDate(document.createdAt)}</p>
        </div>
      </div>
    </div>
  );
};

export default DocumentPreviewModal;