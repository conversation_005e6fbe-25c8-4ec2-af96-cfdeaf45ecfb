// src/services/healthCheckService.js
const prisma = require('../utils/prisma');
const cacheService = require('./cacheService');
const loggerService = require('./loggerService');
const axios = require('axios');

/**
 * Serviço de Health Checks para monitoramento da aplicação
 * Verifica status de componentes críticos do sistema
 */
class HealthCheckService {
  constructor() {
    this.checks = new Map();
    this.initializeChecks();
  }

  /**
   * <PERSON><PERSON>alizar todos os health checks
   */
  initializeChecks() {
    // Database check
    this.checks.set('database', {
      name: 'PostgreSQL Database',
      check: this.checkDatabase.bind(this),
      critical: true,
      timeout: 5000
    });

    // Redis cache check
    this.checks.set('redis', {
      name: 'Redis Cache',
      check: this.checkRedis.bind(this),
      critical: true,
      timeout: 3000
    });

    // External APIs check
    this.checks.set('stripe', {
      name: 'Stripe API',
      check: this.checkStripe.bind(this),
      critical: false,
      timeout: 5000
    });

    // File system check
    this.checks.set('filesystem', {
      name: 'File System',
      check: this.checkFileSystem.bind(this),
      critical: true,
      timeout: 2000
    });

    // Memory usage check
    this.checks.set('memory', {
      name: 'Memory Usage',
      check: this.checkMemory.bind(this),
      critical: false,
      timeout: 1000
    });

    // Disk space check
    this.checks.set('disk', {
      name: 'Disk Space',
      check: this.checkDiskSpace.bind(this),
      critical: false,
      timeout: 2000
    });
  }

  /**
   * Executar todos os health checks
   */
  async runAllChecks() {
    const results = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {}
    };

    const checkPromises = Array.from(this.checks.entries()).map(async ([key, config]) => {
      try {
        const startTime = Date.now();
        
        // Executar check com timeout
        const checkResult = await Promise.race([
          config.check(),
          this.timeoutPromise(config.timeout)
        ]);

        const duration = Date.now() - startTime;

        results.checks[key] = {
          name: config.name,
          status: 'healthy',
          duration,
          critical: config.critical,
          ...checkResult
        };

      } catch (error) {
        results.checks[key] = {
          name: config.name,
          status: 'unhealthy',
          critical: config.critical,
          error: error.message,
          timestamp: new Date().toISOString()
        };

        // Se é um check crítico e falhou, marcar sistema como unhealthy
        if (config.critical) {
          results.status = 'unhealthy';
        }

        loggerService.error('HEALTH_CHECK_FAILED', {
          check: key,
          error: error.message,
          critical: config.critical
        });
      }
    });

    await Promise.all(checkPromises);

    // Log do resultado geral
    loggerService.info('HEALTH_CHECK_COMPLETED', {
      status: results.status,
      totalChecks: Object.keys(results.checks).length,
      failedChecks: Object.values(results.checks).filter(c => c.status === 'unhealthy').length
    });

    return results;
  }

  /**
   * Executar check específico
   */
  async runCheck(checkName) {
    const config = this.checks.get(checkName);
    if (!config) {
      throw new Error(`Health check '${checkName}' não encontrado`);
    }

    try {
      const startTime = Date.now();
      const result = await Promise.race([
        config.check(),
        this.timeoutPromise(config.timeout)
      ]);
      const duration = Date.now() - startTime;

      return {
        name: config.name,
        status: 'healthy',
        duration,
        critical: config.critical,
        ...result
      };
    } catch (error) {
      return {
        name: config.name,
        status: 'unhealthy',
        critical: config.critical,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Check do banco de dados
   */
  async checkDatabase() {
    try {
      // Teste de conexão simples
      await prisma.$queryRaw`SELECT 1 as test`;
      
      // Verificar se consegue contar registros de uma tabela básica
      const userCount = await prisma.user.count();
      
      return {
        details: {
          connection: 'ok',
          userCount,
          provider: 'postgresql'
        }
      };
    } catch (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  /**
   * Check do Redis
   */
  async checkRedis() {
    try {
      if (!cacheService.connected) {
        throw new Error('Redis not connected');
      }

      // Teste de escrita e leitura
      const testKey = 'health_check_test';
      const testValue = Date.now().toString();
      
      await cacheService.set(testKey, testValue, 10);
      const retrievedValue = await cacheService.get(testKey);
      
      if (retrievedValue !== testValue) {
        throw new Error('Redis read/write test failed');
      }

      // Limpar chave de teste
      await cacheService.delete(testKey);

      return {
        details: {
          connection: 'ok',
          readWrite: 'ok'
        }
      };
    } catch (error) {
      throw new Error(`Redis check failed: ${error.message}`);
    }
  }

  /**
   * Check da API do Stripe
   */
  async checkStripe() {
    try {
      if (!process.env.STRIPE_SECRET_KEY) {
        return {
          details: {
            status: 'not_configured',
            message: 'Stripe not configured'
          }
        };
      }

      // Teste simples da API do Stripe
      const response = await axios.get('https://api.stripe.com/v1/account', {
        headers: {
          'Authorization': `Bearer ${process.env.STRIPE_SECRET_KEY}`
        },
        timeout: 3000
      });

      return {
        details: {
          connection: 'ok',
          accountId: response.data.id
        }
      };
    } catch (error) {
      throw new Error(`Stripe API check failed: ${error.message}`);
    }
  }

  /**
   * Check do sistema de arquivos
   */
  async checkFileSystem() {
    const fs = require('fs').promises;
    const path = require('path');

    try {
      const uploadDir = process.env.UPLOAD_PATH || './uploads';
      const testFile = path.join(uploadDir, 'health_check_test.txt');
      const testContent = 'health check test';

      // Verificar se diretório existe
      await fs.access(uploadDir);

      // Teste de escrita
      await fs.writeFile(testFile, testContent);

      // Teste de leitura
      const content = await fs.readFile(testFile, 'utf8');
      if (content !== testContent) {
        throw new Error('File read/write test failed');
      }

      // Limpar arquivo de teste
      await fs.unlink(testFile);

      return {
        details: {
          uploadDir,
          readWrite: 'ok'
        }
      };
    } catch (error) {
      throw new Error(`File system check failed: ${error.message}`);
    }
  }

  /**
   * Check de uso de memória
   */
  async checkMemory() {
    const memUsage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const freeMemory = require('os').freemem();

    const memoryUsagePercent = ((memUsage.heapUsed / memUsage.heapTotal) * 100).toFixed(2);
    const systemMemoryUsagePercent = (((totalMemory - freeMemory) / totalMemory) * 100).toFixed(2);

    // Alertar se uso de memória estiver muito alto
    if (memoryUsagePercent > 90) {
      throw new Error(`High memory usage: ${memoryUsagePercent}%`);
    }

    return {
      details: {
        heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
        memoryUsagePercent: `${memoryUsagePercent}%`,
        systemMemoryUsagePercent: `${systemMemoryUsagePercent}%`,
        rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`
      }
    };
  }

  /**
   * Check de espaço em disco
   */
  async checkDiskSpace() {
    try {
      const fs = require('fs');
      const stats = fs.statSync('.');
      
      // Simulação básica - em produção usar biblioteca específica
      return {
        details: {
          status: 'ok',
          message: 'Disk space check requires additional implementation'
        }
      };
    } catch (error) {
      throw new Error(`Disk space check failed: ${error.message}`);
    }
  }

  /**
   * Promise com timeout
   */
  timeoutPromise(ms) {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Health check timeout after ${ms}ms`)), ms);
    });
  }

  /**
   * Obter status resumido (para load balancer)
   */
  async getSimpleStatus() {
    try {
      // Checks críticos apenas
      const criticalChecks = ['database', 'redis'];
      
      for (const checkName of criticalChecks) {
        const result = await this.runCheck(checkName);
        if (result.status === 'unhealthy') {
          return { status: 'unhealthy' };
        }
      }

      return { status: 'healthy' };
    } catch (error) {
      return { status: 'unhealthy' };
    }
  }
}

// Criar instância singleton
const healthCheckService = new HealthCheckService();

module.exports = healthCheckService;
