const { exec } = require('child_process');
const { promisify } = require('util');
const { backupData } = require('./backup-data');
const { restoreData } = require('./restore-data');

const execAsync = promisify(exec);

async function migrateWithBackup() {
  try {
    console.log('🚀 MIGRAÇÃO COMPLETA COM BACKUP E RESTAURAÇÃO\n');
    console.log('=' .repeat(60));

    // 1. Fazer backup dos dados
    console.log('📦 PASSO 1: Fazendo backup dos dados...');
    const backupFile = await backupData();
    console.log(`✅ Backup salvo em: ${backupFile}`);

    // 2. Resetar o banco de dados
    console.log('\n🗃️  PASSO 2: Resetando banco de dados...');
    try {
      await execAsync('npx prisma migrate reset --force');
      console.log('✅ Banco resetado com sucesso');
    } catch (error) {
      console.log('⚠️  Erro no reset, tentando push force...');
      await execAsync('npx prisma db push --force-reset');
      console.log('✅ Banco resetado com push force');
    }

    // 3. Aplicar novas migrations
    console.log('\n🔄 PASSO 3: Aplicando novas migrations...');
    await execAsync('npx prisma migrate deploy');
    console.log('✅ Migrations aplicadas');

    // 4. Gerar cliente Prisma
    console.log('\n⚙️  PASSO 4: Gerando cliente Prisma...');
    await execAsync('npx prisma generate');
    console.log('✅ Cliente Prisma gerado');

    // 5. Executar seed básico
    console.log('\n🌱 PASSO 5: Executando seed básico...');
    try {
      await execAsync('npx prisma db seed');
      console.log('✅ Seed executado');
    } catch (error) {
      console.log('⚠️  Seed falhou, continuando...');
    }

    // 6. Restaurar dados do backup
    console.log('\n🔄 PASSO 6: Restaurando dados do backup...');
    await restoreData(backupFile);
    console.log('✅ Dados restaurados');

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!');
    console.log('📁 Backup mantido em:', backupFile);
    console.log('⚠️  Lembre-se: Senhas foram redefinidas para "Teste@123"');
    console.log('🔧 Próximos passos:');
    console.log('   1. Verificar se tudo está funcionando');
    console.log('   2. Atualizar senhas se necessário');
    console.log('   3. Testar funcionalidades principais');

  } catch (error) {
    console.error('❌ Erro na migração:', error);
    console.log('\n🆘 RECUPERAÇÃO:');
    console.log('1. Verifique os logs acima');
    console.log('2. Se necessário, restaure manualmente com:');
    console.log('   node restore-data.js <caminho-do-backup>');
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  migrateWithBackup();
}

module.exports = { migrateWithBackup };
