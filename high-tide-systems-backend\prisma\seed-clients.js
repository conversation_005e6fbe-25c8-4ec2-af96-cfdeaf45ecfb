// prisma/seed-clients.js
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const axios = require('axios');
const prisma = new PrismaClient();

// Lista de CEPs reais de diferentes cidades brasileiras
const realCEPs = [
  '01310200', // São Paulo - SP (Av. Paulista)
  '22031001', // Rio de Janeiro - RJ (Copacabana)
  '30130110', // Belo Horizonte - MG (Centro)
  '40010020', // Salvador - BA (Centro)
  '50030230', // Recife - PE (Boa Vista)
  '90010280', // Porto Alegre - RS (Centro)
  '80010010', // Curitiba - PR (Centro)
  '60060170', // Fortaleza - CE (Aldeota)
  '70070120', // Brasília - DF (Asa Sul)
  '69010060', // Manaus - AM (Centro)
  '29055340', // Vitória - ES (Praia do Canto)
  '66010020', // Belém - PA (Campina)
  '59020030', // Natal - RN (Petrópolis)
  '58010000', // <PERSON> (Centro)
  '49010040', // Aracaju - SE (Centro)
  '64000290', // Teresina - PI (Centro)
  '78005370', // Cuiabá - MT (Centro)
  '79002170', // Campo Grande - MS (Centro)
  '74030010', // Goiânia - GO (Setor Central)
  '69900100'  // Rio Branco - AC (Centro)
];

// Função para gerar um nome aleatório
function getRandomName() {
  const firstNames = [
    'Ana', 'João', 'Maria', 'Pedro', 'Carla', 'Lucas', 'Juliana', 'Marcos', 'Fernanda', 'Rafael',
    'Patrícia', 'Bruno', 'Camila', 'Gustavo', 'Mariana', 'Ricardo', 'Aline', 'Thiago', 'Daniela', 'Felipe',
    'Luciana', 'Eduardo', 'Beatriz', 'Rodrigo', 'Amanda', 'Carlos', 'Larissa', 'Paulo', 'Natália', 'Marcelo'
  ];

  const lastNames = [
    'Silva', 'Santos', 'Oliveira', 'Souza', 'Pereira', 'Costa', 'Rodrigues', 'Almeida', 'Nascimento', 'Lima',
    'Araújo', 'Fernandes', 'Carvalho', 'Gomes', 'Martins', 'Rocha', 'Ribeiro', 'Alves', 'Monteiro', 'Mendes',
    'Barros', 'Freitas', 'Barbosa', 'Pinto', 'Moura', 'Cavalcanti', 'Dias', 'Castro', 'Campos', 'Cardoso'
  ];

  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName1 = lastNames[Math.floor(Math.random() * lastNames.length)];
  const lastName2 = lastNames[Math.floor(Math.random() * lastNames.length)];

  return `${firstName} ${lastName1} ${lastName2}`;
}

// Função para gerar um CPF válido
function generateCPF() {
  const n1 = Math.floor(Math.random() * 10);
  const n2 = Math.floor(Math.random() * 10);
  const n3 = Math.floor(Math.random() * 10);
  const n4 = Math.floor(Math.random() * 10);
  const n5 = Math.floor(Math.random() * 10);
  const n6 = Math.floor(Math.random() * 10);
  const n7 = Math.floor(Math.random() * 10);
  const n8 = Math.floor(Math.random() * 10);
  const n9 = Math.floor(Math.random() * 10);

  let d1 = n9 * 2 + n8 * 3 + n7 * 4 + n6 * 5 + n5 * 6 + n4 * 7 + n3 * 8 + n2 * 9 + n1 * 10;
  d1 = 11 - (d1 % 11);
  if (d1 >= 10) d1 = 0;

  let d2 = d1 * 2 + n9 * 3 + n8 * 4 + n7 * 5 + n6 * 6 + n5 * 7 + n4 * 8 + n3 * 9 + n2 * 10 + n1 * 11;
  d2 = 11 - (d2 % 11);
  if (d2 >= 10) d2 = 0;

  return `${n1}${n2}${n3}${n4}${n5}${n6}${n7}${n8}${n9}${d1}${d2}`;
}

// Função para gerar um email a partir do nome
function generateEmail(name) {
  const normalizedName = name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/\s+/g, '.');

  const domains = ['gmail.com', 'hotmail.com', 'outlook.com', 'yahoo.com.br', 'uol.com.br'];
  const domain = domains[Math.floor(Math.random() * domains.length)];

  return `${normalizedName}@${domain}`;
}

// Função para gerar um login a partir do nome
function generateLogin(name) {
  const normalizedName = name
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .split(' ')[0];

  const randomNum = Math.floor(Math.random() * 1000);

  return `${normalizedName}${randomNum}`;
}

// Função para gerar um número de telefone
function generatePhone() {
  const ddd = Math.floor(Math.random() * 89) + 11; // DDD entre 11 e 99
  const part1 = Math.floor(Math.random() * 9000) + 1000;
  const part2 = Math.floor(Math.random() * 9000) + 1000;

  return `(${ddd}) 9${part1}-${part2}`;
}

// Função para gerar uma data de nascimento (entre 18 e 80 anos)
function generateBirthDate() {
  const now = new Date();
  const minAge = 18;
  const maxAge = 80;

  const minYear = now.getFullYear() - maxAge;
  const maxYear = now.getFullYear() - minAge;

  const year = Math.floor(Math.random() * (maxYear - minYear + 1)) + minYear;
  const month = Math.floor(Math.random() * 12);
  const day = Math.floor(Math.random() * 28) + 1; // Evita problemas com meses com menos de 31 dias

  return new Date(year, month, day);
}

// Função para buscar endereço a partir do CEP usando a API ViaCEP
async function getAddressByCEP(cep) {
  try {
    const response = await axios.get(`https://viacep.com.br/ws/${cep}/json/`);

    if (response.data.erro) {
      throw new Error('CEP não encontrado');
    }

    return {
      cep: response.data.cep,
      logradouro: response.data.logradouro,
      complemento: response.data.complemento,
      bairro: response.data.bairro,
      localidade: response.data.localidade,
      uf: response.data.uf
    };
  } catch (error) {
    console.error(`Erro ao buscar CEP ${cep}:`, error.message);
    // Retorna um endereço padrão em caso de erro
    return {
      cep: cep,
      logradouro: 'Rua Exemplo',
      complemento: '',
      bairro: 'Centro',
      localidade: 'São Paulo',
      uf: 'SP'
    };
  }
}

// Função para gerar um número aleatório entre min e max (inclusive)
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Função principal
async function main() {
  console.log('Iniciando seed de clientes...');

  try {
    // Buscar todas as empresas
    const companies = await prisma.company.findMany({
      where: {
        NOT: {
          id: '00000000-0000-0000-0000-000000000001' // Excluir a empresa de teste
        },
        active: true
      },
      include: {
        insurances: true // Incluir os convênios de cada empresa
      }
    });

    console.log(`Encontradas ${companies.length} empresas para adicionar clientes`);

    // Buscar um usuário administrador para cada empresa para ser o criador dos clientes
    const adminUsers = [];

    for (const company of companies) {
      const admin = await prisma.user.findFirst({
        where: {
          companyId: company.id,
          role: {
            in: ['SYSTEM_ADMIN', 'COMPANY_ADMIN']
          },
          active: true
        }
      });

      if (admin) {
        adminUsers.push(admin);
      } else {
        // Se não encontrar um admin, busca qualquer usuário da empresa
        const anyUser = await prisma.user.findFirst({
          where: {
            companyId: company.id,
            active: true
          }
        });

        if (anyUser) {
          adminUsers.push(anyUser);
        } else {
          console.log(`Nenhum usuário encontrado para a empresa ${company.name}. Pulando...`);
          continue;
        }
      }
    }

    // Senha padrão para todos os clientes: 123456
    const defaultPassword = await bcrypt.hash('123456', 10);

    // Para cada empresa, criar entre 10 e 20 clientes
    for (let i = 0; i < companies.length; i++) {
      const company = companies[i];
      const admin = adminUsers[i];

      if (!admin) {
        console.log(`Sem usuário administrador para a empresa ${company.name}. Pulando...`);
        continue;
      }

      console.log(`\nProcessando empresa: ${company.name} (ID: ${company.id})`);

      // Determinar quantos clientes criar (entre 10 e 20)
      const numClients = getRandomInt(10, 20);
      console.log(`Criando ${numClients} clientes para a empresa ${company.name}`);

      // Criar os clientes
      for (let j = 0; j < numClients; j++) {
        try {
          // Gerar dados do cliente
          const fullName = getRandomName();
          const email = generateEmail(fullName);
          const login = generateLogin(fullName);
          const cpf = generateCPF();
          const phone = generatePhone();
          const birthDate = generateBirthDate();

          // Obter um CEP aleatório da lista
          const randomCEP = realCEPs[Math.floor(Math.random() * realCEPs.length)];

          // Buscar endereço pelo CEP
          const address = await getAddressByCEP(randomCEP);

          // Verificar se o cliente já existe pelo email ou login
          const existingClient = await prisma.client.findFirst({
            where: {
              OR: [
                { email },
                { login }
              ]
            }
          });

          if (existingClient) {
            console.log(`Cliente com email ${email} ou login ${login} já existe. Pulando...`);
            continue;
          }

          // Verificar se o CPF já existe
          const existingPerson = await prisma.person.findUnique({
            where: { cpf }
          });

          if (existingPerson) {
            console.log(`Pessoa com CPF ${cpf} já existe. Pulando...`);
            continue;
          }

          // Criar o cliente e a pessoa associada em uma transação
          const result = await prisma.$transaction(async (prisma) => {
            // Criar cliente
            const client = await prisma.client.create({
              data: {
                login,
                email,
                password: defaultPassword,
                createdById: admin.id,
                companyId: company.id
              }
            });

            // Criar pessoa associada ao cliente
            const personData = await prisma.person.create({
              data: {
                fullName,
                cpf,
                birthDate,
                address: address.logradouro,
                neighborhood: address.bairro,
                city: address.localidade,
                state: address.uf,
                postalCode: address.cep.replace('-', ''),
                phone,
                email,
                gender: Math.random() > 0.5 ? 'M' : 'F',
                notes: `Cliente gerado automaticamente para a empresa ${company.name}`,
                relationship: 'Titular',
                createdById: admin.id
              }
            });

            // Criar relacionamento ClientPerson
            await prisma.clientPerson.create({
              data: {
                clientId: client.id,
                personId: personData.id,
                relationship: 'Titular',
                isPrimary: true
              }
            });

            // Se a empresa tiver convênios, associar aleatoriamente 1 ou 2 convênios ao cliente
            if (company.insurances && company.insurances.length > 0) {
              // Determinar quantos convênios associar (1 ou 2)
              const numInsurances = Math.min(getRandomInt(1, 2), company.insurances.length);

              // Embaralhar os convênios da empresa
              const shuffledInsurances = [...company.insurances].sort(() => 0.5 - Math.random());

              // Associar os convênios ao cliente
              for (let k = 0; k < numInsurances; k++) {
                const insurance = shuffledInsurances[k];

                await prisma.clientInsurance.create({
                  data: {
                    clientId: client.id,
                    insuranceId: insurance.id,
                    policyNumber: `${Math.floor(Math.random() * 1000000)}`,
                    validUntil: new Date(new Date().setFullYear(new Date().getFullYear() + 1)), // Validade de 1 ano
                    notes: `Convênio associado automaticamente ao cliente ${fullName}`
                  }
                });

                // Também associar o convênio à pessoa
                await prisma.personInsurance.create({
                  data: {
                    personId: personData.id,
                    insuranceId: insurance.id,
                    policyNumber: `${Math.floor(Math.random() * 1000000)}`,
                    validUntil: new Date(new Date().setFullYear(new Date().getFullYear() + 1)), // Validade de 1 ano
                    notes: `Convênio associado automaticamente à pessoa ${fullName}`
                  }
                });
              }
            }

            return { client, personData };
          });

          console.log(`✅ Cliente criado: ${result.personData.fullName} (ID: ${result.client.id})`);
        } catch (error) {
          console.error(`Erro ao criar cliente para empresa ${company.name}:`, error);
        }
      }
    }

    console.log('\nSeed de clientes concluído com sucesso!');
  } catch (error) {
    console.error('Erro durante o seed de clientes:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Erro durante o seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
