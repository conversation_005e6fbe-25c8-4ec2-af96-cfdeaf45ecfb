"use client";

import React, { useState, useRef } from "react";
import { X, Upload, FileText, Loader2 } from "lucide-react";
import { documentsService } from "@/app/modules/people/services/documentsService";

const DocumentUploadModal = ({ 
  isOpen, 
  onClose, 
  documentType, 
  targetId, 
  targetType = "person", 
  onSuccess 
}) => {
  const fileInputRef = useRef(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  
  // Reset do estado quando o modal é aberto ou fechado
  React.useEffect(() => {
    if (!isOpen) {
      // Limpar estado quando o modal é fechado
      setSelectedFile(null);
      setIsUploading(false);
      setUploadProgress(0);
      setError(null);
    }
  }, [isOpen]);
  
  // Verificar se temos um tipo de documento válido
  if (!documentType && isOpen) {
    console.error("DocumentUploadModal: documentType é obrigatório");
    onClose();
    return null;
  }

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
      setError(null);
      setUploadProgress(0); // Resetar progresso ao escolher novo arquivo
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !documentType) {
      setError("Selecione um arquivo para upload");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    const formData = new FormData();
    formData.append("documents", selectedFile);
    formData.append("types", JSON.stringify([documentType]));

    try {
      // Usar o serviço de documentos para fazer o upload
      await documentsService.uploadDocument(
        formData, 
        targetType, 
        targetId, 
        (progress) => setUploadProgress(progress)
      );
      
      // Após upload bem-sucedido
      onSuccess && onSuccess();
      
      // Limpar estados antes de fechar o modal
      setSelectedFile(null);
      setIsUploading(false);
      setUploadProgress(0);
      
      // Fechar o modal
      onClose();
    } catch (err) {
      console.error("Error uploading document:", err);
      setError(err.message || "Erro ao fazer upload do documento");
      setIsUploading(false);
    }
  };

  const getDocumentTypeDisplay = (type) => {
    const typeMap = {
      "RG": "RG",
      "CPF": "CPF",
      "CNH": "Carteira de Motorista",
      "COMP_RESIDENCIA": "Comprovante de Residência",
      "CERTIDAO_NASCIMENTO": "Certidão de Nascimento",
      "CERTIDAO_CASAMENTO": "Certidão de Casamento",
      "CARTAO_VACINACAO": "Cartão de Vacinação",
      "PASSAPORTE": "Passaporte",
      "TITULO_ELEITOR": "Título de Eleitor",
      "CARTEIRA_TRABALHO": "Carteira de Trabalho",
      "OUTROS": "Outros"
    };
    
    return typeMap[type] || type;
  };
  
  // Determina o tipo MIME apropriado com base na extensão do arquivo
  const getMimeTypeFromFilename = (filename) => {
    const extension = filename.split('.').pop().toLowerCase();
    const mimeTypes = {
      'pdf': 'application/pdf',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'txt': 'text/plain'
    };
    
    return mimeTypes[extension] || 'application/octet-stream';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[11050]">
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-gray-100">
            Upload de {getDocumentTypeDisplay(documentType)}
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            <div className="border-2 border-dashed border-neutral-300 dark:border-gray-600 rounded-lg p-4 text-center">
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                onChange={handleFileChange}
                accept=".pdf,.jpg,.jpeg,.png"
              />
              {selectedFile ? (
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2 text-primary-600 dark:text-primary-400">
                    <FileText size={24} />
                    <span className="font-medium">{selectedFile.name}</span>
                  </div>
                  <p className="text-neutral-500 dark:text-gray-400 text-sm">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                  <button
                    onClick={() => setSelectedFile(null)}
                    className="px-3 py-1 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg text-sm hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    Alterar arquivo
                  </button>
                </div>
              ) : (
                <div onClick={() => fileInputRef.current.click()} className="cursor-pointer py-6">
                  <Upload className="h-10 w-10 mx-auto text-neutral-400 dark:text-gray-500 mb-2" />
                  <p className="text-neutral-600 dark:text-gray-300 mb-1">Clique para selecionar um arquivo</p>
                  <p className="text-neutral-500 dark:text-gray-400 text-sm">PDF, JPG ou PNG (máx. 10MB)</p>
                </div>
              )}
            </div>
            
            {error && (
              <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg text-sm">
                {error}
              </div>
            )}
            
            {isUploading && (
              <div className="space-y-2">
                <div className="h-2 w-full bg-neutral-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-primary-500 dark:bg-primary-600 rounded-full" 
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-center text-neutral-600 dark:text-gray-300 text-sm">
                  {uploadProgress}% concluído
                </p>
              </div>
            )}
            
            <div className="flex justify-end gap-3 pt-2">
              <button
                onClick={() => {
                  // Resetar todos os estados ao cancelar
                  setSelectedFile(null);
                  setIsUploading(false);
                  setUploadProgress(0);
                  setError(null);
                  onClose();
                }}
                className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
                disabled={isUploading}
              >
                Cancelar
              </button>
              <button
                onClick={handleUpload}
                className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
                disabled={!selectedFile || isUploading}
              >
                {isUploading ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    <span>Enviando...</span>
                  </>
                ) : uploadProgress === 100 ? (
                  <>
                    <span>Enviado com sucesso!</span>
                  </>
                ) : (
                  <>
                    <Upload size={16} />
                    <span>Enviar</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUploadModal;