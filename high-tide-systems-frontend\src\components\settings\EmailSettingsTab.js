"use client";

import React, { useState, useEffect } from "react";
import {
  Mail,
  Plus,
  Edit,
  Trash,
  AlertTriangle,
  Check,
  Loader2,
  Lock,
  Server,
  Send,
  CheckCircle,
  X
} from "lucide-react";
import { api } from "@/utils/api";
import { useAuth } from "@/contexts/AuthContext";

const EmailSettingsTab = () => {
  const { user } = useAuth();
  const [emailConfigs, setEmailConfigs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [modalOpen, setModalOpen] = useState(false);
  const [testModalOpen, setTestModalOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [testingConfig, setTestingConfig] = useState(null);
  const [testEmail, setTestEmail] = useState("");
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [loadingCompanies, setLoadingCompanies] = useState(false);
  const [selectedCompanyId, setSelectedCompanyId] = useState("");

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';
  // For system admin, use the selected company, otherwise use the user's company
  const companyId = isSystemAdmin ? selectedCompanyId : user?.companyId;

  // Form state for new/edit email config
  const [formData, setFormData] = useState({
    smtpHost: "",
    smtpPort: "587",
    smtpSecure: false,
    smtpUser: "",
    smtpPassword: "",
    emailFromName: "",
    emailFromAddress: "",
    active: true
  });

  useEffect(() => {
    // If system admin, load the companies for the dropdown
    if (isSystemAdmin) {
      loadCompanies();
    }
  }, [isSystemAdmin]);

  useEffect(() => {
    if (companyId) {
      loadEmailConfigs();
    } else {
      setEmailConfigs([]);
    }
  }, [companyId]);
  
  // Load companies for system admin
  const loadCompanies = async () => {
    setLoadingCompanies(true);
    try {
      const response = await api.get("/companies", {
        params: { 
          active: true,
          limit: 100 // Get all active companies
        }
      });
      
      setCompanies(response.data.companies || []);
      
      // If there are companies, select the first one by default
      if (response.data.companies && response.data.companies.length > 0) {
        setSelectedCompanyId(response.data.companies[0].id);
      }
    } catch (error) {
      console.error("Error loading companies:", error);
      setError("Falha ao carregar empresas: " + (error.response?.data?.message || ""));
    } finally {
      setLoadingCompanies(false);
    }
  };

  const loadEmailConfigs = async () => {
    setIsLoading(true);
    setError("");
    
    try {
      let targetCompanyId = companyId;
      
      // If system admin and no company selected, just show message
      if (isSystemAdmin && !targetCompanyId) {
        setEmailConfigs([]);
        setIsLoading(false);
        return;
      }
      
      const response = await api.get("/email-configs", {
        params: { 
          companyId: targetCompanyId
        }
      });
      
      setEmailConfigs(response.data.emailConfigs || []);
    } catch (err) {
      console.error("Error loading email configurations:", err);
      setError("Falha ao carregar configurações de email. " + (err.response?.data?.message || ""));
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const resetForm = () => {
    setFormData({
      smtpHost: "",
      smtpPort: "587",
      smtpSecure: false,
      smtpUser: "",
      smtpPassword: "",
      emailFromName: "",
      emailFromAddress: "",
      active: true
    });
    setEditingConfig(null);
  };

  const openAddModal = () => {
    resetForm();
    setModalOpen(true);
  };

  const openEditModal = (config) => {
    setFormData({
      smtpHost: config.smtpHost,
      smtpPort: config.smtpPort.toString(),
      smtpSecure: config.smtpSecure,
      smtpUser: config.smtpUser,
      smtpPassword: "", // Password is not returned from API for security
      emailFromName: config.emailFromName,
      emailFromAddress: config.emailFromAddress,
      active: config.active
    });
    setEditingConfig(config);
    setModalOpen(true);
  };

  const validateForm = () => {
    // Basic validation
    if (!formData.smtpHost) return "Servidor SMTP é obrigatório";
    if (!formData.smtpPort) return "Porta SMTP é obrigatória";
    if (!formData.smtpUser) return "Usuário SMTP é obrigatório";
    if (!formData.emailFromName) return "Nome do remetente é obrigatório";
    if (!formData.emailFromAddress) return "Email do remetente é obrigatório";
    
    // Validate email format
    if (!/^\S+@\S+\.\S+$/.test(formData.emailFromAddress)) {
      return "Formato de email inválido";
    }
    
    // If editing and no password is provided, it's ok (we keep the old one)
    if (!editingConfig && !formData.smtpPassword) {
      return "Senha SMTP é obrigatória";
    }
    
    return null; // No errors
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }
    
    if (!companyId) {
      setError("É necessário selecionar uma empresa");
      return;
    }
    
    setIsSaving(true);
    setError("");
    setSuccess("");
    
    try {
      // Use the currently selected company
      const targetCompanyId = companyId;
      
      // Prepare data
      const data = {
        ...formData,
        companyId: targetCompanyId,
        smtpPort: parseInt(formData.smtpPort)
      };
      
      // If editing and no password provided, remove password field
      if (editingConfig && !formData.smtpPassword) {
        delete data.smtpPassword;
      }
      
      let response;
      if (editingConfig) {
        // Update existing config
        response = await api.put(`/email-configs/${editingConfig.id}`, data);
        setSuccess("Configuração de email atualizada com sucesso!");
      } else {
        // Create new config
        response = await api.post('/email-configs', data);
        setSuccess("Configuração de email criada com sucesso!");
      }
      
      // Reload configs
      await loadEmailConfigs();
      
      // Close modal
      setModalOpen(false);
      resetForm();
    } catch (err) {
      console.error("Error saving email configuration:", err);
      setError("Falha ao salvar configuração de email: " + (err.response?.data?.message || ""));
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async (configId) => {
    if (!confirm("Tem certeza que deseja excluir esta configuração de email?")) {
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      await api.delete(`/email-configs/${configId}`);
      setSuccess("Configuração de email excluída com sucesso!");
      await loadEmailConfigs();
    } catch (err) {
      console.error("Error deleting email configuration:", err);
      setError("Falha ao excluir configuração de email: " + (err.response?.data?.message || ""));
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (config) => {
    setIsLoading(true);
    setError("");
    
    try {
      await api.patch(`/email-configs/${config.id}/toggle-status`);
      setSuccess(`Configuração de email ${config.active ? 'desativada' : 'ativada'} com sucesso!`);
      await loadEmailConfigs();
    } catch (err) {
      console.error("Error toggling email configuration status:", err);
      setError("Falha ao alterar status da configuração: " + (err.response?.data?.message || ""));
    } finally {
      setIsLoading(false);
    }
  };

  const openTestModal = (config) => {
    setTestingConfig(config);
    setTestEmail(user?.email || "");
    setTestResult(null);
    setTestModalOpen(true);
  };
  
  // Helper function to get company name
  const getCompanyName = (id) => {
    if (!id) return "";
    const company = companies.find(c => c.id === id);
    return company ? company.name : "";
  };

  const handleTestEmail = async (e) => {
    e.preventDefault();
    
    if (!testEmail || !/^\S+@\S+\.\S+$/.test(testEmail)) {
      setError("Email de teste inválido");
      return;
    }
    
    setIsTesting(true);
    setError("");
    setTestResult(null);
    
    try {
      const response = await api.post(`/email-configs/${testingConfig.id}/test`, {
        testEmail
      });
      
      setTestResult({
        success: true,
        message: "Email enviado com sucesso!"
      });
    } catch (err) {
      console.error("Error testing email configuration:", err);
      setTestResult({
        success: false,
        message: "Falha ao enviar email: " + (err.response?.data?.message || "Erro desconhecido"),
        details: err.response?.data?.details || null
      });
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-base font-medium text-neutral-800 dark:text-gray-100 flex items-center gap-2">
          <Mail className="h-5 w-5 text-primary-500 dark:text-primary-400" />
          Configurações de Email
        </h3>
        
        <button
          onClick={openAddModal}
          className="flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          disabled={isLoading || !companyId}
        >
          <Plus className="h-4 w-4" />
          <span>Nova Configuração</span>
        </button>
      </div>
      
      {/* Company Selector for System Admin */}
      {isSystemAdmin && (
        <div className="mb-6 mt-4">
          <label className="block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-2" htmlFor="companySelector">
            Selecionar Empresa
          </label>
          <div className="flex gap-2 items-center">
            <select
              id="companySelector"
              value={selectedCompanyId}
              onChange={(e) => setSelectedCompanyId(e.target.value)}
              className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white"
              disabled={loadingCompanies}
            >
              <option value="">Selecione uma empresa</option>
              {companies.map(company => (
                <option key={company.id} value={company.id}>
                  {company.name}
                </option>
              ))}
            </select>
            
            {loadingCompanies && (
              <Loader2 className="h-5 w-5 animate-spin text-primary-500 dark:text-primary-400 ml-2" />
            )}
          </div>
          <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
            Como administrador do sistema, você pode gerenciar as configurações de email de qualquer empresa.
          </p>
        </div>
      )}
      
      {/* Info / Warning Messages */}
      {!selectedCompanyId && isSystemAdmin && (
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-md p-4 text-amber-800 dark:text-amber-300">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 mr-2 mt-0.5" />
            <p className="text-sm">
              Por favor, selecione uma empresa para configurar seus emails.
            </p>
          </div>
        </div>
      )}
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-md p-4 text-red-700 dark:text-red-300">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 mr-2 mt-0.5" />
            <p className="text-sm">{error}</p>
          </div>
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/50 rounded-md p-4 text-green-700 dark:text-green-300">
          <div className="flex items-start">
            <Check className="h-5 w-5 mr-2 mt-0.5" />
            <p className="text-sm">{success}</p>
          </div>
        </div>
      )}
      
      {/* Email Configurations List */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="h-6 w-6 animate-spin text-primary-500 dark:text-primary-400" />
        </div>
      ) : emailConfigs.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 border border-neutral-100 dark:border-gray-700 rounded-lg p-6 text-center">
          <Mail className="h-12 w-12 mx-auto text-neutral-300 dark:text-gray-600 mb-3" />
          <h3 className="text-lg font-medium text-neutral-800 dark:text-white mb-2">Nenhuma configuração de email</h3>
          <p className="text-neutral-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
            Configure os parâmetros de SMTP para enviar emails através do sistema, como notificações, 
            confirmações e relatórios.
          </p>
          <button
            onClick={openAddModal}
            className="inline-flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
            disabled={!companyId}
          >
            <Plus className="h-4 w-4" />
            <span>Adicionar Configuração de Email</span>
          </button>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 border border-neutral-100 dark:border-gray-700 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
              <thead className="bg-neutral-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                    Nome do Remetente
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                    Email do Remetente
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                    Servidor SMTP
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700">
                {emailConfigs.map((config) => (
                  <tr key={config.id} className="hover:bg-neutral-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-800 dark:text-white">
                      {config.emailFromName}
                      {isSystemAdmin && (
                        <div className="text-xs text-neutral-500 dark:text-gray-400 mt-1">
                          {getCompanyName(config.companyId)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-gray-300">
                      {config.emailFromAddress}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-gray-300">
                      <div className="flex items-center">
                        <Server className="h-4 w-4 mr-1 text-neutral-400 dark:text-gray-500" />
                        <span>{config.smtpHost}:{config.smtpPort}</span>
                        {config.smtpSecure && (
                          <Lock className="h-4 w-4 ml-2 text-green-500 dark:text-green-400" title="SSL/TLS Enabled" />
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          config.active
                            ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                            : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                        }`}
                      >
                        {config.active ? "Ativo" : "Inativo"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => openTestModal(config)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 px-2 py-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/30"
                          title="Testar Configuração"
                        >
                          <Send className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => openEditModal(config)}
                          className="text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white px-2 py-1 rounded hover:bg-neutral-50 dark:hover:bg-gray-700"
                          title="Editar"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleToggleActive(config)}
                          className={`px-2 py-1 rounded hover:bg-neutral-50 dark:hover:bg-gray-700 ${
                            config.active
                              ? "text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300"
                              : "text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                          }`}
                          title={config.active ? "Desativar" : "Ativar"}
                        >
                          {config.active ? (
                            <AlertTriangle className="h-4 w-4" />
                          ) : (
                            <Check className="h-4 w-4" />
                          )}
                        </button>
                        <button
                          onClick={() => handleDelete(config.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 px-2 py-1 rounded hover:bg-red-50 dark:hover:bg-red-900/30"
                          title="Excluir"
                        >
                          <Trash className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      
      {/* Modal for Add/Edit Email Config */}
      {modalOpen && (
        <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
          <div className="fixed inset-0 bg-black/50" onClick={() => setModalOpen(false)}></div>
          
          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-2xl w-full z-[11050]">
            <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-neutral-800 dark:text-white flex items-center gap-2">
                <Mail className="h-5 w-5 text-primary-500 dark:text-primary-400" />
                {editingConfig ? "Editar Configuração de Email" : "Nova Configuração de Email"}
              </h3>
              <button
                onClick={() => setModalOpen(false)}
                className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                {/* SMTP Host */}
                <div>
                  <label htmlFor="smtpHost" className="block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1">
                    Servidor SMTP <span className="text-red-500 dark:text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    id="smtpHost"
                    name="smtpHost"
                    placeholder="smtp.example.com"
                    value={formData.smtpHost}
                    onChange={handleInputChange}
                    className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white"
                    required
                  />
                </div>
                
                {/* SMTP Port */}
                <div>
                  <label htmlFor="smtpPort" className="block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1">
                    Porta SMTP <span className="text-red-500 dark:text-red-400">*</span>
                  </label>
                  <input
                    type="number"
                    id="smtpPort"
                    name="smtpPort"
                    placeholder="587"
                    value={formData.smtpPort}
                    onChange={handleInputChange}
                    className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white"
                    required
                  />
                  <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                    Portas comuns: 25, 465 (SSL), 587 (TLS/STARTTLS)
                  </p>
                </div>
                
                {/* SMTP User */}
                <div>
                  <label htmlFor="smtpUser" className="block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1">
                    Usuário SMTP <span className="text-red-500 dark:text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    id="smtpUser"
                    name="smtpUser"
                    placeholder="<EMAIL>"
                    value={formData.smtpUser}
                    onChange={handleInputChange}
                    className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white"
                    required
                  />
                </div>
                
                {/* SMTP Password */}
                <div>
                  <label htmlFor="smtpPassword" className="block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1">
                    Senha SMTP {!editingConfig && <span className="text-red-500 dark:text-red-400">*</span>}
                  </label>
                  <input
                    type="password"
                    id="smtpPassword"
                    name="smtpPassword"
                    placeholder={editingConfig ? "••••••••••••" : "Senha"}
                    value={formData.smtpPassword}
                    onChange={handleInputChange}
                    className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white"
                    required={!editingConfig}
                  />
                  {editingConfig && (
                    <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                      Deixe em branco para manter a senha atual
                    </p>
                  )}
                </div>
                
                {/* Email From Name */}
                <div>
                  <label htmlFor="emailFromName" className="block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1">
                    Nome do Remetente <span className="text-red-500 dark:text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    id="emailFromName"
                    name="emailFromName"
                    placeholder="High Tide"
                    value={formData.emailFromName}
                    onChange={handleInputChange}
                    className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white"
                    required
                  />
                </div>
                
                {/* Email From Address */}
                <div>
                  <label htmlFor="emailFromAddress" className="block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1">
                    Email do Remetente <span className="text-red-500 dark:text-red-400">*</span>
                  </label>
                  <input
                    type="email"
                    id="emailFromAddress"
                    name="emailFromAddress"
                    placeholder="<EMAIL>"
                    value={formData.emailFromAddress}
                    onChange={handleInputChange}
                    className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white"
                    required
                  />
                </div>
                
                {/* SMTP Secure */}
                <div className="col-span-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="smtpSecure"
                      name="smtpSecure"
                      checked={formData.smtpSecure}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-400 border-neutral-300 dark:border-gray-600 rounded"
                    />
                    <label htmlFor="smtpSecure" className="ml-2 block text-sm text-neutral-700 dark:text-gray-200">
                      Usar SSL/TLS (geralmente para porta 465)
                    </label>
                  </div>
                  <p className="mt-1 ml-6 text-xs text-neutral-500 dark:text-gray-400">
                    Se desativado, STARTTLS será utilizado quando disponível (recomendado para porta 587)
                  </p>
                </div>
                
                {/* Active Status */}
                <div className="col-span-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="active"
                      name="active"
                      checked={formData.active}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-400 border-neutral-300 dark:border-gray-600 rounded"
                    />
                    <label htmlFor="active" className="ml-2 block text-sm text-neutral-700 dark:text-gray-200">
                      Configuração ativa
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => setModalOpen(false)}
                  className="px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Cancelar
                </button>
                
                <button
                  type="submit"
                  className="flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:pointer-events-none"
                  disabled={isSaving}
                >
                  {isSaving && <Loader2 className="h-4 w-4 animate-spin" />}
                  <span>{editingConfig ? "Atualizar" : "Salvar"}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      
      {/* Modal for Testing Email */}
      {testModalOpen && testingConfig && (
        <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
          <div className="fixed inset-0 bg-black/50" onClick={() => setTestModalOpen(false)}></div>
          
          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-md w-full z-[11050]">
            <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-neutral-800 dark:text-white flex items-center gap-2">
                <Send className="h-5 w-5 text-primary-500 dark:text-primary-400" />
                Testar Configuração de Email
              </h3>
              <button
                onClick={() => setTestModalOpen(false)}
                className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="p-6">
              <div className="mb-4">
                <label htmlFor="testEmail" className="block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1">
                  Email para Teste
                </label>
                <input
                  type="email"
                  id="testEmail"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white"
                  disabled={isTesting}
                />
                <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                  Um email de teste será enviado para este endereço
                </p>
              </div>
              
              <div className="mb-4 p-3 bg-neutral-50 dark:bg-gray-700 border border-neutral-200 dark:border-gray-600 rounded-lg text-sm">
                <h4 className="font-medium text-neutral-800 dark:text-white mb-1">Configuração a ser testada:</h4>
                <p className="text-neutral-600 dark:text-gray-300"><strong>Servidor:</strong> {testingConfig.smtpHost}:{testingConfig.smtpPort}</p>
                <p className="text-neutral-600 dark:text-gray-300"><strong>Usuário:</strong> {testingConfig.smtpUser}</p>
                <p className="text-neutral-600 dark:text-gray-300">
                  <strong>Remetente:</strong> {testingConfig.emailFromName} &lt;{testingConfig.emailFromAddress}&gt;
                </p>
              </div>
              
              {testResult && (
                <div className={`mb-4 p-3 border rounded-lg ${
                  testResult.success 
                    ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800/50 text-green-700 dark:text-green-300" 
                    : "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-300"
                }`}>
                  <div className="flex items-start">
                    {testResult.success ? (
                      <CheckCircle className="h-5 w-5 mr-2 mt-0.5 text-green-500 dark:text-green-400" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 text-red-500 dark:text-red-400" />
                    )}
                    <div>
                      <p className="font-medium">{testResult.message}</p>
                      {testResult.details && (
                        <p className="mt-1 text-xs font-mono overflow-auto max-h-40">
                          {typeof testResult.details === 'object' 
                            ? JSON.stringify(testResult.details, null, 2) 
                            : testResult.details}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
              
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setTestModalOpen(false)}
                  className="px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Fechar
                </button>
                
                <button
                  onClick={handleTestEmail}
                  className="flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:pointer-events-none"
                  disabled={isTesting || !testEmail}
                >
                  {isTesting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Enviando...</span>
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      <span>Enviar Email de Teste</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailSettingsTab;