// src/config/stripe.js
const Stripe = require('stripe');
const { SystemModule } = require('@prisma/client');

// Inicializa o cliente Stripe com a chave secreta
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  maxNetworkRetries: 2, // Retry automaticamente em caso de falha de rede
  timeout: 10000, // 10 segundos de timeout
});

// Configuração de preços baseada na landing page
const PRICING_CONFIG = {
  basePrice: 19.90, // Preço base por usuário/mês
  annualDiscount: 0.10, // 10% de desconto para pagamento anual

  // Descontos por quantidade de usuários
  userDiscounts: [
    { minUsers: 200, discount: 40 },
    { minUsers: 100, discount: 35 },
    { minUsers: 50, discount: 25 },
    { minUsers: 20, discount: 15 },
    { minUsers: 5, discount: 10 },
    { minUsers: 1, discount: 0 }
  ]
};

// Função para calcular desconto baseado na quantidade de usuários
const getDiscountByUserCount = (users) => {
  for (const tier of PRICING_CONFIG.userDiscounts) {
    if (users >= tier.minUsers) {
      return tier.discount;
    }
  }
  return 0;
};

// Função para calcular preços
const calculatePrice = (users, isAnnual = false) => {
  const discount = getDiscountByUserCount(users);
  const totalWithoutDiscount = users * PRICING_CONFIG.basePrice;
  const discountAmount = totalWithoutDiscount * (discount / 100);
  const monthlyPrice = totalWithoutDiscount - discountAmount;

  // Preço anual com desconto adicional
  const yearlyPriceWithoutAnnualDiscount = monthlyPrice * 12;
  const yearlyPrice = yearlyPriceWithoutAnnualDiscount * (1 - PRICING_CONFIG.annualDiscount);

  return {
    totalWithoutDiscount,
    discountAmount,
    discount,
    monthlyPrice,
    yearlyPrice,
    yearlyPriceWithoutAnnualDiscount,
    annualDiscount: PRICING_CONFIG.annualDiscount * 100,
    pricePerUser: monthlyPrice / users,
    finalPrice: isAnnual ? yearlyPrice : monthlyPrice
  };
};

// Definir os módulos (agora apenas para referência, preços calculados dinamicamente)
const MODULES = {
  // Módulos que estão sempre incluídos no plano básico
  [SystemModule.BASIC]: {
    name: 'Módulo Básico',
    description: 'Acesso às funcionalidades básicas da plataforma',
    included: true, // Sempre incluído em qualquer assinatura
  },
  [SystemModule.ADMIN]: {
    name: 'Administração',
    description: 'Recursos administrativos completos',
    included: true, // Sempre incluído em qualquer assinatura
  },
  [SystemModule.SCHEDULING]: {
    name: 'Agendamento',
    description: 'Sistema completo de agendamento e gerenciamento de consultas',
    included: true, // Incluído no plano básico
  },

  // Módulos adicionais (não vendidos atualmente)
  [SystemModule.RH]: {
    name: 'Recursos Humanos',
    description: 'Gerenciamento completo de funcionários, controle de ponto, folha de pagamento',
    included: false, // Módulo adicional (não vendido)
  },
  [SystemModule.FINANCIAL]: {
    name: 'Financeiro',
    description: 'Controle financeiro, contas a pagar e receber, relatórios',
    included: false, // Módulo adicional (não vendido)
  },
};

// Módulos incluídos no plano básico
const BASIC_INCLUDED_MODULES = [
  SystemModule.BASIC,
  SystemModule.ADMIN,
  SystemModule.SCHEDULING
];

module.exports = {
  stripe,
  MODULES,
  BASIC_INCLUDED_MODULES,
  PRICING_CONFIG,
  getDiscountByUserCount,
  calculatePrice,
};