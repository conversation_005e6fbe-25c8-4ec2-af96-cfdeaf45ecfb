// src/services/stripeService.js
const { stripe, MODULES, BASIC_INCLUDED_MODULES, calculatePrice } = require('../config/stripe');
const {
    PrismaClient,
    SystemModule,
    BillingCycle,
    SubscriptionStatus,
    InvoiceStatus,
    Prisma,
} = require('@prisma/client');
const prisma = new PrismaClient();

// Helper method to find a company admin for audit logs
async function findCompanyAdmin(companyId) {
    // Find an admin user for this company to associate with the audit log
    const adminUser = await prisma.user.findFirst({
        where: {
            companyId,
            role: 'COMPANY_ADMIN',
            active: true
        }
    });

    return adminUser;
}

// Helper method to safely create audit logs
async function safelyCreateAuditLog(data) {
    // Only create audit log if we have a valid user ID
    if (data.userId) {
        await prisma.auditLog.create({ data });
    } else {
        // If no userId provided, try to find an admin for the company
        if (data.companyId) {
            const adminUser = await findCompanyAdmin(data.companyId);

            if (adminUser) {
                await prisma.auditLog.create({
                    data: {
                        ...data,
                        userId: adminUser.id
                    }
                });
            } else {
                // If no admin user found, log a warning but continue processing
                console.warn(`No admin user found for company ${data.companyId} to associate with audit log`);
            }
        } else {
            console.warn(`Cannot create audit log without userId or companyId: ${JSON.stringify(data)}`);
        }
    }
}

class StripeService {

    /**
     * Cria ou atualiza um cliente Stripe para uma empresa
     */
    async createOrUpdateCustomer(companyId) {
        try {
            const company = await prisma.company.findUnique({
                where: { id: companyId },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            let customer;

            if (company.stripeCustomerId) {
                // Atualiza o cliente existente
                customer = await stripe.customers.update(company.stripeCustomerId, {
                    name: company.legalName || company.name,
                    email: company.contactEmail,
                    metadata: {
                        companyId: company.id,
                        cnpj: company.cnpj,
                    },
                });
            } else {
                // Cria um novo cliente
                customer = await stripe.customers.create({
                    name: company.legalName || company.name,
                    email: company.contactEmail,
                    metadata: {
                        companyId: company.id,
                        cnpj: company.cnpj,
                    },
                });

                // Atualiza a empresa com o ID do cliente Stripe
                await prisma.company.update({
                    where: { id: companyId },
                    data: { stripeCustomerId: customer.id },
                });
            }

            return customer;
        } catch (error) {
            console.error('Erro ao criar/atualizar cliente Stripe:', error);
            throw error;
        }
    }

    /**
     * Cria um produto no Stripe (se ainda não existir)
     * Para cada módulo do sistema, criamos um produto no Stripe
     */
    async createProductIfNotExists(moduleType) {
        try {
            const moduleInfo = MODULES[moduleType];

            if (!moduleInfo) {
                throw new Error(`Módulo não encontrado: ${moduleType}`);
            }

            // Verifica se o produto já existe no Stripe
            const existingProducts = await stripe.products.list({
                limit: 100,
            });

            const existingProduct = existingProducts.data.find(
                (product) => product.metadata.moduleType === moduleType
            );

            if (existingProduct) {
                return existingProduct;
            }

            // Cria um novo produto
            const product = await stripe.products.create({
                name: moduleInfo.name,
                description: moduleInfo.description,
                metadata: {
                    moduleType: moduleType,
                },
                active: true,
            });

            return product;
        } catch (error) {
            console.error('Erro ao criar produto Stripe:', error);
            throw error;
        }
    }

    /**
     * Cria um preço no Stripe para o plano básico
     */
    async createPrice(productId, userCount, billingCycle = BillingCycle.MONTHLY) {
        try {
            // Calcula o preço baseado na quantidade de usuários
            const pricing = calculatePrice(userCount, billingCycle === BillingCycle.YEARLY);
            const unitAmount = pricing.finalPrice * 100; // Stripe trabalha em centavos

            const price = await stripe.prices.create({
                product: productId,
                unit_amount: Math.round(unitAmount),
                currency: 'brl',
                recurring: {
                    interval: billingCycle === BillingCycle.YEARLY ? 'year' : 'month',
                },
                metadata: {
                    type: 'basic_plan',
                    userCount: userCount.toString(),
                    billingCycle: billingCycle,
                    discount: pricing.discount.toString(),
                },
            });

            return price;
        } catch (error) {
            console.error('Erro ao criar preço Stripe:', error);
            throw error;
        }
    }

    /**
     * Obtém ou cria um preço para o plano básico
     */
    async getPriceForBasicPlan(userCount, billingCycle = BillingCycle.MONTHLY) {
        try {
            // Lista todos os preços ativos no Stripe
            const prices = await stripe.prices.list({
                active: true,
                limit: 100,
            });

            // Billingcycle para string para comparação com metadata
            const billingCycleStr = billingCycle === BillingCycle.YEARLY ? 'YEARLY' : 'MONTHLY';

            // Busca por um preço existente para a quantidade de usuários e ciclo
            const existingPrice = prices.data.find(
                (price) =>
                    price.metadata.type === 'basic_plan' &&
                    price.metadata.userCount === userCount.toString() &&
                    price.metadata.billingCycle === billingCycleStr
            );

            if (existingPrice) {
                return existingPrice;
            }

            // Se não encontrar, cria um novo produto e preço
            const product = await this.createProductIfNotExists('BASIC_PLAN');
            const newPrice = await this.createPrice(product.id, userCount, billingCycle);

            return newPrice;
        } catch (error) {
            console.error('Erro ao obter preço para plano básico:', error);
            throw error;
        }
    }

    /**
     * Cria uma sessão de checkout para uma nova assinatura
     */
    async createCheckoutSession(companyId, billingCycleInput = 'monthly', userLimit = 5) {
        try {
            // Converte string para enum
            const billingCycle = billingCycleInput.toUpperCase() === 'YEARLY'
                ? BillingCycle.YEARLY
                : BillingCycle.MONTHLY;

            const company = await prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    subscription: true,
                },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            // Verifica se já existe uma assinatura ativa
            if (company.subscription && company.subscription.active) {
                throw new Error('A empresa já possui uma assinatura ativa');
            }

            // Cria ou obtém o cliente Stripe
            let customer;
            if (company.stripeCustomerId) {
                customer = { id: company.stripeCustomerId };
            } else {
                customer = await this.createOrUpdateCustomer(companyId);
            }

            // Calcula o preço baseado na quantidade de usuários
            const pricing = calculatePrice(userLimit, billingCycle === BillingCycle.YEARLY);

            console.log(`[STRIPE] Criando checkout para ${userLimit} usuários: R$ ${pricing.finalPrice.toFixed(2)}`);

            // Cria um produto único para o plano básico
            const product = await stripe.products.create({
                name: `High Tide Systems - ${userLimit} usuário${userLimit > 1 ? 's' : ''}`,
                description: `Plano básico com acesso completo para ${userLimit} usuário${userLimit > 1 ? 's' : ''}`,
                metadata: {
                    includedModules: BASIC_INCLUDED_MODULES.join(','),
                    userLimit: userLimit.toString(),
                    discount: pricing.discount.toString()
                }
            });

            // Cria um preço para o plano básico
            const price = await stripe.prices.create({
                product: product.id,
                unit_amount: Math.round(pricing.finalPrice * 100), // Em centavos
                currency: 'brl',
                recurring: {
                    interval: billingCycle === BillingCycle.YEARLY ? 'year' : 'month',
                },
                metadata: {
                    type: 'basic_plan',
                    billingCycle: billingCycle,
                    userCount: userLimit.toString(),
                    discount: pricing.discount.toString(),
                    basePrice: pricing.totalWithoutDiscount.toString(),
                },
            });

            // Cria a sessão de checkout
            const session = await stripe.checkout.sessions.create({
                payment_method_types: ['card'],
                customer: customer.id,
                line_items: [{
                    price: price.id,
                    quantity: 1,
                }],
                mode: 'subscription',
                success_url: `${process.env.FRONTEND_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
                cancel_url: `${process.env.FRONTEND_URL}/subscription/cancel`,
                metadata: {
                    companyId: companyId,
                    billingCycle: billingCycle,
                    userLimit: userLimit.toString(),
                    finalPrice: pricing.finalPrice.toString(),
                    includedModules: BASIC_INCLUDED_MODULES.join(','),
                },
            });

            return session;
        } catch (error) {
            console.error('Erro ao criar sessão de checkout:', error);
            throw error;
        }
    }

    /**
     * Processa o webhook de evento de checkout.session.completed
     */
    async handleCheckoutCompleted(event) {
        try {
            const session = event.data.object;
            const customerId = session.customer;
            const subscriptionId = session.subscription;
            const metadata = session.metadata || {};

            // Extract company ID from metadata
            const companyId = metadata.companyId;
            if (!companyId) {
                console.error('Company ID not found in session metadata');
                return;
            }

            // Get company details
            const company = await prisma.company.findUnique({
                where: { id: companyId },
            });

            if (!company) {
                console.error(`Company not found with ID: ${companyId}`);
                return;
            }

            // Get subscription details from Stripe
            const stripeSubscription = await this.stripe.subscriptions.retrieve(subscriptionId);

            // Extract billing cycle from subscription
            const billingCycle = stripeSubscription.items.data[0]?.price.recurring.interval === 'year'
                ? 'YEARLY'
                : 'MONTHLY';

            // Update the customer ID if needed
            if (company.stripeCustomerId !== customerId) {
                await prisma.company.update({
                    where: { id: companyId },
                    data: { stripeCustomerId: customerId },
                });
            }

            // Check if a subscription already exists for this company
            const existingSubscription = await prisma.subscription.findUnique({
                where: { companyId },
            });

            if (existingSubscription) {
                console.log(`Subscription already exists for company ${companyId}, updating...`);

                // Update existing subscription
                const updated = await prisma.subscription.update({
                    where: { id: existingSubscription.id },
                    data: {
                        status: 'ACTIVE',
                        billingCycle,
                        stripeCustomerId: customerId,
                        stripeSubscriptionId: subscriptionId,
                        stripeCurrentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
                        cancelAtPeriodEnd: false,
                    },
                });

                return updated;
            }

            // Extract active modules from subscription items
            const subscribedProducts = stripeSubscription.items.data.map(item => item.price.product);

            // Get all module types to include (basic modules + subscribed products)
            const moduleTypes = [...BASIC_INCLUDED_MODULES];

            // Add premium modules based on subscribed products
            // Here, you would map Stripe products to SystemModule types
            // This is a placeholder - implement your actual logic
            for (const product of subscribedProducts) {
                // Get product metadata from Stripe to identify the module
                const stripeProduct = await this.stripe.products.retrieve(product);
                const moduleMetadata = stripeProduct.metadata || {};

                if (moduleMetadata.moduleType && !moduleTypes.includes(moduleMetadata.moduleType)) {
                    moduleTypes.push(moduleMetadata.moduleType);
                }
            }

            // Create Subscription record in database
            const subscription = await prisma.subscription.create({
                data: {
                    companyId,
                    status: 'ACTIVE',
                    billingCycle,
                    stripeCustomerId: customerId,
                    stripeSubscriptionId: subscriptionId,
                    stripeCurrentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),

                    // Create module records
                    modules: {
                        create: moduleTypes.map(moduleType => ({
                            moduleType,
                            active: true,
                            // Todos os módulos do plano básico têm preço 0 (incluídos no preço total)
                            pricePerMonth: 0,
                        })),
                    },
                },
            });

            // Use the helper method to safely create an audit log
            await safelyCreateAuditLog({
                action: "CREATE",
                entityType: "Subscription",
                entityId: subscription.id,
                details: {
                    billingCycle,
                    modules: moduleTypes
                },
                companyId,
            });

            return subscription;
        } catch (error) {
            console.error('Erro ao processar checkout completo:', error);
            throw error;
        }
    }

    /**
     * Adiciona um módulo a uma assinatura existente
     */
    async addModuleToSubscription(companyId, moduleType) {
        try {
            // Verifica se a empresa existe e tem uma assinatura ativa
            const company = await prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    subscription: {
                        include: {
                            modules: true,
                        },
                    },
                },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            if (!company.subscription || !company.subscription.active) {
                throw new Error('Empresa não possui assinatura ativa');
            }

            // Verifica se o módulo já está ativo
            const existingModule = company.subscription.modules.find(
                (m) => m.moduleType === moduleType && m.active
            );

            if (existingModule) {
                throw new Error(`Módulo ${moduleType} já está ativo na assinatura`);
            }

            // Verifica se é um ID de teste (não faz chamadas reais para o Stripe)
            const isTestSubscription = company.subscription.stripeSubscriptionId?.startsWith('sub_test_');

            let price = null;

            if (!isTestSubscription) {
                // Obtém o preço para o módulo
                price = await this.getPriceForModule(
                    moduleType,
                    company.subscription.billingCycle
                );

                // Obtém a assinatura no Stripe
                const stripeSubscription = await stripe.subscriptions.retrieve(
                    company.subscription.stripeSubscriptionId
                );

                // Adiciona o item à assinatura do Stripe
                await stripe.subscriptionItems.create({
                    subscription: stripeSubscription.id,
                    price: price.id,
                    quantity: 1,
                });
            } else {
                console.log(`[TEST MODE] Simulando adição do módulo ${moduleType} à assinatura de teste`);
            }

            // Adiciona ou atualiza o módulo no banco de dados
            const moduleInDb = await prisma.subscriptionModule.findFirst({
                where: {
                    subscriptionId: company.subscription.id,
                    moduleType: moduleType,
                },
            });

            if (moduleInDb) {
                // Atualiza o módulo existente
                await prisma.subscriptionModule.update({
                    where: { id: moduleInDb.id },
                    data: {
                        active: true,
                        stripePriceId: price?.id || null, // Para test subscriptions, price pode ser null
                        pricePerMonth: new Prisma.Decimal(0), // Módulos básicos incluídos no preço total
                        addedAt: new Date(),
                    },
                });
            } else {
                // Cria um novo registro de módulo
                await prisma.subscriptionModule.create({
                    data: {
                        subscriptionId: company.subscription.id,
                        moduleType: moduleType,
                        active: true,
                        stripePriceId: price?.id || null, // Para test subscriptions, price pode ser null
                        pricePerMonth: new Prisma.Decimal(0), // Módulos básicos incluídos no preço total
                    },
                });
            }

            // Para módulos básicos, não altera o preço (já incluído)
            // Para módulos adicionais (que não vendemos mais), preço permanece o mesmo
            const currentPrice = parseFloat(company.subscription.pricePerMonth);

            await prisma.subscription.update({
                where: { id: company.subscription.id },
                data: {
                    pricePerMonth: new Prisma.Decimal(currentPrice), // Mantém o preço atual
                },
            });

            // Registra a ação no log de auditoria - será feito pelo controller
            // Não criamos log aqui para evitar problemas com userId null

            return {
                success: true,
                message: `Módulo ${MODULES[moduleType].name} adicionado com sucesso`,
            };
        } catch (error) {
            console.error('Erro ao adicionar módulo à assinatura:', error);
            throw error;
        }
    }

    /**
     * Remove um módulo de uma assinatura
     * Nota: BASIC, ADMIN e SCHEDULING não podem ser removidos
     */
    async removeModuleFromSubscription(companyId, moduleType) {
        try {
            // Verifica se é um módulo que não pode ser removido
            if ([SystemModule.BASIC, SystemModule.ADMIN, SystemModule.SCHEDULING].includes(moduleType)) {
                throw new Error(`O módulo ${moduleType} não pode ser removido da assinatura`);
            }

            // Verifica se a empresa existe e tem uma assinatura ativa
            const company = await prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    subscription: {
                        include: {
                            modules: true,
                        },
                    },
                },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            if (!company.subscription || !company.subscription.active) {
                throw new Error('Empresa não possui assinatura ativa');
            }

            // Verifica se o módulo está ativo
            const existingModule = company.subscription.modules.find(
                (m) => m.moduleType === moduleType && m.active
            );

            if (!existingModule) {
                throw new Error(`Módulo ${moduleType} não está ativo na assinatura`);
            }

            // Verifica se é um ID de teste (não faz chamadas reais para o Stripe)
            const isTestSubscription = company.subscription.stripeSubscriptionId?.startsWith('sub_test_');

            if (!isTestSubscription) {
                // Obtém a assinatura no Stripe (apenas para IDs reais)
                const stripeSubscription = await stripe.subscriptions.retrieve(
                    company.subscription.stripeSubscriptionId,
                    { expand: ['items.data.price'] }
                );

                // Encontra o item da assinatura correspondente ao módulo
                const subscriptionItem = stripeSubscription.items.data.find(
                    (item) => item.price.metadata.moduleType === moduleType
                );

                if (subscriptionItem) {
                    // Remove o item da assinatura do Stripe
                    await stripe.subscriptionItems.del(subscriptionItem.id);
                }
            } else {
                console.log(`[TEST MODE] Simulando remoção do módulo ${moduleType} da assinatura de teste`);
            }

            // Desativa o módulo no banco de dados
            await prisma.subscriptionModule.update({
                where: { id: existingModule.id },
                data: {
                    active: false,
                },
            });

            // Atualiza o preço total da assinatura
            const newPrice = parseFloat(company.subscription.pricePerMonth) - parseFloat(existingModule.pricePerMonth);

            await prisma.subscription.update({
                where: { id: company.subscription.id },
                data: {
                    pricePerMonth: new Prisma.Decimal(Math.max(0, newPrice)), // Garante que o preço não seja negativo
                },
            });

            // Registra a ação no log de auditoria - será feito pelo controller
            // Não criamos log aqui para evitar problemas com userId null

            return {
                success: true,
                message: `Módulo ${MODULES[moduleType].name} removido com sucesso`,
            };
        } catch (error) {
            console.error('Erro ao remover módulo da assinatura:', error);
            throw error;
        }
    }

    /**
     * Cancela uma assinatura
     */
    async cancelSubscription(companyId, cancelAtPeriodEnd = true) {
        try {
            // Verifica se a empresa existe e tem uma assinatura ativa
            const company = await prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    subscription: true,
                },
            });

            if (!company) {
                throw new Error(`Empresa não encontrada: ${companyId}`);
            }

            if (!company.subscription || !company.subscription.active) {
                throw new Error('Empresa não possui assinatura ativa');
            }

            // Verifica se é um ID de teste (não faz chamadas reais para o Stripe)
            const isTestSubscription = company.subscription.stripeSubscriptionId?.startsWith('sub_test_');

            if (!isTestSubscription) {
                // Cancela a assinatura no Stripe (apenas para IDs reais)
                await stripe.subscriptions.update(company.subscription.stripeSubscriptionId, {
                    cancel_at_period_end: cancelAtPeriodEnd,
                });
            } else {
                console.log(`[TEST MODE] Simulando cancelamento da assinatura de teste`);
            }

            // Atualiza o status da assinatura no banco de dados
            await prisma.subscription.update({
                where: { id: company.subscription.id },
                data: {
                    cancelAtPeriodEnd: cancelAtPeriodEnd,
                    // Se cancelAtPeriodEnd é false, então o cancelamento é imediato
                    status: cancelAtPeriodEnd ? SubscriptionStatus.ACTIVE : SubscriptionStatus.CANCELED,
                    active: cancelAtPeriodEnd, // Mantém ativo se o cancelamento for no fim do período
                    endDate: cancelAtPeriodEnd ? null : new Date(),
                },
            });

            // Registra a ação no log de auditoria - será feito pelo controller
            // Não criamos log aqui para evitar problemas com userId null

            return {
                success: true,
                message: cancelAtPeriodEnd
                    ? 'Assinatura será cancelada no fim do período atual'
                    : 'Assinatura cancelada com sucesso',
            };
        } catch (error) {
            console.error('Erro ao cancelar assinatura:', error);
            throw error;
        }
    }

    /**
     * Processa webhooks do Stripe
     */
    async handleWebhook(event) {
        try {
            // Check the event type and call the appropriate handler
            switch (event.type) {
                case 'checkout.session.completed':
                    return await this.handleCheckoutCompleted(event);

                case 'customer.subscription.updated':
                    return await this.handleCustomerSubscriptionUpdated(event);

                case 'customer.subscription.deleted':
                    return await this.handleCustomerSubscriptionDeleted(event);

                case 'invoice.paid':
                    return await this.handleInvoicePaid(event);

                // Add more event handlers here as needed

                default:
                    console.log(`Evento não tratado: ${event.type}`);
                    return;
            }
        } catch (error) {
            console.error(`Erro ao processar webhook do Stripe (${event.type}):`, error);
            throw error;
        }
    }
    /**
     * Processa o evento de invoice paga
     */
    async handleInvoicePaid(event) {
        try {
            const invoice = event.data.object;
            const stripeSubscriptionId = invoice.subscription;

            if (!stripeSubscriptionId) {
                console.log('Invoice not related to a subscription');
                return;
            }

            // Find the subscription in our database
            const dbSubscription = await prisma.subscription.findFirst({
                where: { stripeSubscriptionId }
            });

            if (!dbSubscription) {
                console.error(`Subscription not found for Stripe ID: ${stripeSubscriptionId}`);
                return;
            }

            // Create an invoice record
            const newInvoice = await prisma.invoice.create({
                data: {
                    subscriptionId: dbSubscription.id,
                    companyId: dbSubscription.companyId,
                    amount: Number(invoice.amount_paid) / 100, // Convert from cents
                    status: 'PAID',
                    dueDate: new Date(invoice.due_date * 1000 || Date.now()),
                    paidAt: new Date(invoice.status_transitions.paid_at * 1000 || Date.now()),
                    stripeInvoiceId: invoice.id,
                    stripeInvoiceUrl: invoice.hosted_invoice_url,
                },
            });

            // Use the helper method to safely create an audit log
            await safelyCreateAuditLog({
                action: "CREATE",
                entityType: "Invoice",
                entityId: newInvoice.id,
                details: {
                    amount: Number(invoice.amount_paid) / 100,
                    status: 'PAID',
                },
                companyId: dbSubscription.companyId,
            });

            return newInvoice;
        } catch (error) {
            console.error('Erro ao processar fatura paga:', error);
            throw error;
        }
    }

    // Handle customer subscription updated webhook
    async handleCustomerSubscriptionUpdated(event) {
        try {
            const subscription = event.data.object;
            const stripeSubscriptionId = subscription.id;

            // Find the subscription in our database
            const dbSubscription = await prisma.subscription.findFirst({
                where: { stripeSubscriptionId },
                include: { modules: true }
            });

            if (!dbSubscription) {
                console.error(`Subscription not found for Stripe ID: ${stripeSubscriptionId}`);
                return;
            }

            // Extract billing cycle and status
            const billingCycle = subscription.items.data[0]?.plan.interval === 'year'
                ? 'YEARLY'
                : 'MONTHLY';

            // Map Stripe status to our status
            let status = 'ACTIVE';
            if (subscription.status === 'past_due') status = 'PAST_DUE';
            else if (subscription.status === 'canceled') status = 'CANCELED';
            else if (subscription.status === 'incomplete') status = 'INCOMPLETE';
            else if (subscription.status === 'trialing') status = 'TRIAL';

            // Update the subscription
            const updated = await prisma.subscription.update({
                where: { id: dbSubscription.id },
                data: {
                    status,
                    billingCycle,
                    cancelAtPeriodEnd: subscription.cancel_at_period_end,
                    stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
                },
            });

            // Use the helper method to safely create an audit log
            await safelyCreateAuditLog({
                action: "UPDATE",
                entityType: "Subscription",
                entityId: dbSubscription.id,
                details: {
                    status,
                    billingCycle,
                    cancelAtPeriodEnd: subscription.cancel_at_period_end,
                },
                companyId: dbSubscription.companyId,
            });

            return updated;
        } catch (error) {
            console.error('Erro ao processar atualização de assinatura:', error);
            throw error;
        }
    }

    // Handle customer subscription deleted webhook
    async handleCustomerSubscriptionDeleted(event) {
        try {
            const subscription = event.data.object;
            const stripeSubscriptionId = subscription.id;

            // Find the subscription in our database
            const dbSubscription = await prisma.subscription.findFirst({
                where: { stripeSubscriptionId }
            });

            if (!dbSubscription) {
                console.error(`Subscription not found for Stripe ID: ${stripeSubscriptionId}`);
                return;
            }

            // Update the subscription status
            const updated = await prisma.subscription.update({
                where: { id: dbSubscription.id },
                data: {
                    status: 'CANCELED',
                    endDate: new Date(),
                },
            });

            // Use the helper method to safely create an audit log
            await safelyCreateAuditLog({
                action: "UPDATE",
                entityType: "Subscription",
                entityId: dbSubscription.id,
                details: {
                    status: 'CANCELED',
                    endDate: new Date().toISOString(),
                },
                companyId: dbSubscription.companyId,
            });

            return updated;
        } catch (error) {
            console.error('Erro ao processar exclusão de assinatura:', error);
            throw error;
        }
    }

    /**
     * Verifica se uma empresa tem acesso a um determinado módulo
     */
    async hasModuleAccess(companyId, moduleType) {
        try {
            // SYSTEM_ADMIN sempre tem acesso a tudo
            if (!companyId) {
                return true;
            }

            // Busca a assinatura da empresa
            const subscription = await prisma.subscription.findUnique({
                where: { companyId },
                include: {
                    modules: {
                        where: { active: true },
                    },
                },
            });

            // Se não tiver assinatura ou não estiver ativa, não tem acesso
            if (!subscription || !subscription.active) {
                return false;
            }

            // Verifica se o módulo está ativo
            return subscription.modules.some(m => m.moduleType === moduleType);
        } catch (error) {
            console.error('Erro ao verificar acesso ao módulo:', error);
            throw error;
        }
    }
}

module.exports = new StripeService();