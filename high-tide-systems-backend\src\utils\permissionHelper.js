// src/utils/permissionHelper.js

/**
 * Configuração das permissões do sistema
 * Esta é uma cópia da configuração do frontend para garantir consistência
 */
const PERMISSIONS_CONFIG = {
  // Módulo de Administração
  ADMIN: {
    name: 'Administração',
    permissions: [
      { id: 'admin.users.view' },
      { id: 'admin.users.create' },
      { id: 'admin.users.edit' },
      { id: 'admin.users.delete' },
      { id: 'admin.permissions.manage' },
      { id: 'admin.logs.view' },
      { id: 'admin.config.edit' },
      { id: 'admin.professions.view' },
      { id: 'admin.professions.create' },
      { id: 'admin.professions.edit' },
      { id: 'admin.professions.delete' },
      { id: 'admin.profession-groups.view' },
      { id: 'admin.profession-groups.create' },
      { id: 'admin.profession-groups.edit' },
      { id: 'admin.profession-groups.delete' },
      { id: 'admin.subscription.view' },
      { id: 'admin.subscription.manage' }
    ]
  },

  // Módulo de RH
  RH: {
    name: 'Recursos Humanos',
    permissions: [
      { id: 'rh.dashboard.view' },
      { id: 'rh.employees.view' },
      { id: 'rh.employees.create' },
      { id: 'rh.employees.edit' },
      { id: 'rh.payroll.view' },
      { id: 'rh.payroll.manage' },
      { id: 'rh.benefits.view' },
      { id: 'rh.benefits.manage' },
      { id: 'rh.attendance.view' },
      { id: 'rh.attendance.manage' }
    ]
  },

  // Módulo Financeiro
  FINANCIAL: {
    name: 'Financeiro',
    permissions: [
      { id: 'financial.dashboard.view' },
      { id: 'financial.invoices.view' },
      { id: 'financial.invoices.create' },
      { id: 'financial.invoices.edit' },
      { id: 'financial.payments.view' },
      { id: 'financial.payments.process' },
      { id: 'financial.expenses.view' },
      { id: 'financial.expenses.manage' },
      { id: 'financial.reports.view' },
      { id: 'financial.reports.export' }
    ]
  },

  // Módulo de Agendamento
  SCHEDULING: {
    name: 'Agendamento',
    permissions: [
      { id: 'scheduling.calendar.view' },
      { id: 'scheduling.calendar.create' },
      { id: 'scheduling.calendar.edit' },
      { id: 'scheduling.calendar.delete' },
      { id: 'scheduling.working-hours.view' },
      { id: 'scheduling.working-hours.manage' },
      { id: 'scheduling.service-types.view' },
      { id: 'scheduling.service-types.create' },
      { id: 'scheduling.service-types.edit' },
      { id: 'scheduling.service-types.delete' },
      { id: 'scheduling.locations.view' },
      { id: 'scheduling.locations.create' },
      { id: 'scheduling.locations.edit' },
      { id: 'scheduling.locations.delete' },
      { id: 'scheduling.occupancy.view' },
      { id: 'scheduling.appointments-report.view' },
      { id: 'scheduling.appointments-report.export' },
      { id: 'scheduling.appointments-dashboard.view' },
      { id: 'scheduling.rooms.manage' },
      { id: 'scheduling.resources.manage' }
    ]
  },

  // Módulo de Pessoas
  PEOPLE: {
    name: 'Pessoas',
    permissions: [
      { id: 'people.clients.view' },
      { id: 'people.clients.create' },
      { id: 'people.clients.edit' },
      { id: 'people.clients.delete' },
      { id: 'people.persons.view' },
      { id: 'people.persons.create' },
      { id: 'people.persons.edit' },
      { id: 'people.persons.delete' },
      { id: 'people.insurances.view' },
      { id: 'people.insurances.create' },
      { id: 'people.insurances.edit' },
      { id: 'people.insurances.delete' },
      { id: 'people.insurance-limits.view' },
      { id: 'people.insurance-limits.create' },
      { id: 'people.insurance-limits.edit' },
      { id: 'people.insurance-limits.delete' }
    ]
  },

  // Módulo Básico
  BASIC: {
    name: 'Básico',
    permissions: [
      { id: 'basic.profile.view' },
      { id: 'basic.profile.edit' }
    ]
  }
};

/**
 * Obtém todas as permissões disponíveis no sistema
 * @returns {Array} Array com os IDs de todas as permissões
 */
const getAllPermissions = () => {
  const allPermissions = [];

  Object.keys(PERMISSIONS_CONFIG).forEach(moduleId => {
    const module = PERMISSIONS_CONFIG[moduleId];
    module.permissions.forEach(permission => {
      allPermissions.push(permission.id);
    });
  });

  return allPermissions;
};

/**
 * Obtém todos os módulos disponíveis no sistema
 * @returns {Array} Array com os IDs de todos os módulos
 */
const getAllModules = () => {
  return Object.keys(PERMISSIONS_CONFIG);
};

module.exports = {
  getAllPermissions,
  getAllModules,
  PERMISSIONS_CONFIG
};
