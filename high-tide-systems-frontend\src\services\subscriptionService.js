// services/subscriptionService.js
import { api } from '@/utils/api';

export const subscriptionService = {
  /**
   * Obtém os planos disponíveis
   */
  async getPlans() {
    try {
      const response = await api.get('/subscription/plans');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar planos:', error);
      throw error;
    }
  },

  /**
   * Obtém informações da assinatura atual
   */
  async getSubscription() {
    try {
      const response = await api.get('/subscription/subscription');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar assinatura:', error);
      throw error;
    }
  },

  /**
   * Cria uma sessão de checkout do Stripe
   * @param {Object} options - Opções do checkout
   * @param {string} options.billingCycle - 'monthly' ou 'yearly'
   * @param {number} options.userLimit - Quantidade de usuários
   */
  async createCheckoutSession(options = {}) {
    try {
      const { billingCycle = 'monthly', userLimit = 5 } = options;

      const response = await api.post('/subscription/checkout', {
        billingCycle,
        userLimit
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao criar sessão de checkout:', error);
      throw error;
    }
  },

  /**
   * Adiciona um módulo à assinatura
   * @param {string} moduleType - Tipo do módulo a ser adicionado
   */
  async addModule(moduleType) {
    try {
      const response = await api.post('/subscription/module/add', {
        moduleType
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao adicionar módulo:', error);
      throw error;
    }
  },

  /**
   * Remove um módulo da assinatura
   * @param {string} moduleType - Tipo do módulo a ser removido
   */
  async removeModule(moduleType) {
    try {
      const response = await api.post('/subscription/module/remove', {
        moduleType
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao remover módulo:', error);
      throw error;
    }
  },

  /**
   * Cancela a assinatura
   */
  async cancelSubscription() {
    try {
      const response = await api.post('/subscription/cancel');
      return response.data;
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      throw error;
    }
  },

  /**
   * Obtém as faturas da assinatura
   */
  async getInvoices() {
    try {
      const response = await api.get('/subscription/invoices');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar faturas:', error);
      throw error;
    }
  }
};
