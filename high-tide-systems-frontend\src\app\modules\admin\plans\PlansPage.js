"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { calculateAdditionalUsersCost, formatCurrency } from "@/utils/pricing";
import {
  CreditCard,
  Users,
  Package,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  XCircle,
  Plus,
  Minus,
  RefreshCw,
  Settings,
  Crown,
  Shield,
  Zap,
  Building,
  AlertTriangle,
  Lock,
  Ban,
  X
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/hooks/usePermissions";
import { useToast } from "@/contexts/ToastContext";
import { plansService } from "@/app/modules/admin/services/plansService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { ModuleHeader, ModuleSelect } from "@/components/ui";

const PlansPage = () => {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const { can } = usePermissions();
  const { toast_success, toast_error } = useToast();
  const [planData, setPlanData] = useState(null);
  const [availablePlans, setAvailablePlans] = useState(null);
  const [companies, setCompanies] = useState([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Estados para o modal de adicionar usuários
  const [showAddUsersModal, setShowAddUsersModal] = useState(false);
  const [additionalUsersCount, setAdditionalUsersCount] = useState(1);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelConfirmationText, setCancelConfirmationText] = useState('');

  // Estados para o modal de confirmação de módulos
  const [showModuleConfirmModal, setShowModuleConfirmModal] = useState(false);
  const [moduleAction, setModuleAction] = useState(null); // 'add' ou 'remove'
  const [selectedModule, setSelectedModule] = useState(null);

  // Verificar se o usuário atual é um system_admin
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (!isSystemAdmin) return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
      
      // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira
      if (!selectedCompanyId && response.length > 0) {
        setSelectedCompanyId(response[0].id);
      }
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar as empresas."
      });
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Função para carregar dados do plano
  const loadPlanData = async (forceRefresh = false) => {
    console.log('[DEBUG] ===== INICIANDO loadPlanData =====');
    console.log('[DEBUG] forceRefresh:', forceRefresh);
    console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);
    console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);

    // Para system_admin, não carregar se não tiver empresa selecionada
    if (isSystemAdmin && !selectedCompanyId) {
      console.log('[DEBUG] System admin sem empresa selecionada, não carregando dados');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);

      console.log('[DEBUG] Fazendo chamadas para API...');
      const [planResponse, availablePlansResponse] = await Promise.all([
        plansService.getPlansData(companyId, forceRefresh),
        plansService.getAvailablePlans()
      ]);

      console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');
      console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));
      console.log('[DEBUG] availablePlansResponse completo:', JSON.stringify(availablePlansResponse, null, 2));
      console.log('[DEBUG] Módulos ativos:', planResponse?.modules?.map(m => `${m.moduleType} (${m.active ? 'ATIVO' : 'INATIVO'})`));
      console.log('[DEBUG] Quantidade de módulos:', planResponse?.modules?.length);
      console.log('[DEBUG] availablePlans.modules:', availablePlansResponse?.modules);

      console.log('[DEBUG] ===== ATUALIZANDO ESTADO =====');
      console.log('[DEBUG] Estado anterior planData:', planData?.modules?.map(m => m.moduleType));

      setPlanData(planResponse);
      setAvailablePlans(availablePlansResponse);

      console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');

    } catch (error) {
      console.error("[DEBUG] ===== ERRO AO CARREGAR DADOS =====");
      console.error("Erro ao carregar dados do plano:", error);
      console.error("Error details:", error.response?.data);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar os dados do plano."
      });
    } finally {
      setIsLoading(false);
      console.log('[DEBUG] ===== FIM loadPlanData =====');
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    if (isSystemAdmin) {
      loadCompanies();
    } else {
      loadPlanData();
    }
  }, [isSystemAdmin]);

  // Recarregar dados quando a empresa selecionada mudar
  useEffect(() => {
    if (isSystemAdmin && selectedCompanyId) {
      loadPlanData();
    } else if (!isSystemAdmin) {
      loadPlanData();
    }
  }, [selectedCompanyId, isSystemAdmin]);

  // Monitor planData changes
  useEffect(() => {
    console.log('[DEBUG] ===== PLANDATA MUDOU =====');
    console.log('[DEBUG] planData:', planData);
    console.log('[DEBUG] Módulos no estado:', planData?.modules?.map(m => `${m.moduleType} (${m.active ? 'ATIVO' : 'INATIVO'})`));
    console.log('[DEBUG] ================================');
  }, [planData]);

  // Função para abrir modal de adicionar usuários
  const handleOpenAddUsersModal = () => {
    setAdditionalUsersCount(1);
    setShowAddUsersModal(true);
  };

  // Função para fechar modal de adicionar usuários
  const handleCloseAddUsersModal = () => {
    setShowAddUsersModal(false);
    setAdditionalUsersCount(1);
  };

  // Função para abrir modal de cancelamento
  const handleOpenCancelModal = () => {
    setCancelConfirmationText('');
    setShowCancelModal(true);
  };

  // Função para fechar modal de cancelamento
  const handleCloseCancelModal = () => {
    setShowCancelModal(false);
    setCancelConfirmationText('');
  };

  // Função para calcular o custo adicional usando a função centralizada
  const calculateAdditionalCost = () => {
    if (!planData) return { additionalCost: 0, costPerAdditionalUser: 19.90 };

    const currentUsers = planData.subscription.userLimit;
    const isAnnual = planData.subscription.billingCycle === 'YEARLY';

    return calculateAdditionalUsersCost(currentUsers, additionalUsersCount, isAnnual);
  };

  // Função para calcular preço por usuário atual
  const calculatePricePerUser = () => {
    if (!planData) return 19.90;

    const currentPrice = planData.subscription.pricePerMonth;
    const currentUsers = planData.subscription.userLimit;

    if (currentUsers > 0) {
      return currentPrice / currentUsers;
    }

    return 19.90;
  };

  // Função para adicionar usuários (confirmada pelo modal)
  const handleAddUsers = async () => {
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      await plansService.addUsers(additionalUsersCount, companyId);

      const additionalCost = calculateAdditionalCost();

      toast_success({
        title: "Usuários Adicionados",
        message: `${additionalUsersCount} usuário(s) adicionado(s) ao plano. Custo adicional: R$ ${additionalCost.toFixed(2)}/mês.`
      });

      handleCloseAddUsersModal();
      loadPlanData(true); // Force refresh
    } catch (error) {
      console.error("Erro ao adicionar usuários:", error);
      toast_error({
        title: "Erro",
        message: error.response?.data?.message || "Não foi possível adicionar usuários ao plano."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para abrir modal de confirmação para adicionar módulo
  const openAddModuleConfirmation = (moduleType) => {
    console.log('[DEBUG] openAddModuleConfirmation:', { moduleType, availablePlans });
    console.log('[DEBUG] availablePlans.modules:', availablePlans?.modules);

    if (!availablePlans?.modules) {
      console.error('[DEBUG] availablePlans.modules não está disponível');
      toast_error({
        title: "Erro",
        message: "Dados dos módulos não estão disponíveis. Tente recarregar a página."
      });
      return;
    }

    const moduleInfo = availablePlans.modules[moduleType];
    console.log('[DEBUG] moduleInfo encontrado:', moduleInfo);

    setSelectedModule({ type: moduleType, info: moduleInfo });
    setModuleAction('add');
    setShowModuleConfirmModal(true);
  };

  // Função para abrir modal de confirmação para remover módulo
  const openRemoveModuleConfirmation = (moduleType) => {
    const moduleInfo = planData?.modules?.find(m => m.moduleType === moduleType);
    setSelectedModule({ type: moduleType, info: moduleInfo });
    setModuleAction('remove');
    setShowModuleConfirmModal(true);
  };

  // Função para fechar modal de confirmação de módulos
  const closeModuleConfirmModal = () => {
    setShowModuleConfirmModal(false);
    setModuleAction(null);
    setSelectedModule(null);
  };

  // Função para confirmar a ação do módulo
  const confirmModuleAction = async () => {
    if (!selectedModule || !moduleAction) return;

    closeModuleConfirmModal();

    if (moduleAction === 'add') {
      await handleAddModule(selectedModule.type);
    } else if (moduleAction === 'remove') {
      await handleRemoveModule(selectedModule.type);
    }
  };

  // Função para adicionar módulo
  const handleAddModule = async (moduleType) => {
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      await plansService.addModule(moduleType, companyId);
      toast_success({
        title: "Sucesso",
        message: "Módulo adicionado ao plano com sucesso."
      });

      // Aguardar um pouco para garantir que o cache foi invalidado
      await new Promise(resolve => setTimeout(resolve, 500));
      await loadPlanData(true); // Force refresh
    } catch (error) {
      console.error("Erro ao adicionar módulo:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível adicionar o módulo ao plano."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para remover módulo
  const handleRemoveModule = async (moduleType) => {
    console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      console.log('[DEBUG] Removendo módulo para empresa:', companyId);

      const result = await plansService.removeModule(moduleType, companyId);
      console.log('[DEBUG] Resultado da remoção:', result);

      toast_success({
        title: "Sucesso",
        message: "Módulo removido do plano com sucesso."
      });

      console.log('[DEBUG] Aguardando invalidação de cache...');
      // Aguardar um pouco para garantir que o cache foi invalidado
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('[DEBUG] Recarregando dados do plano...');
      await loadPlanData(true); // Force refresh para evitar cache
      console.log('[DEBUG] Dados recarregados');

    } catch (error) {
      console.error("Erro ao remover módulo:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível remover o módulo do plano."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para cancelar assinatura (confirmada pelo modal)
  const handleCancelSubscription = async () => {
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      await plansService.cancelSubscription(companyId);
      toast_success({
        title: "Assinatura Cancelada",
        message: "Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago."
      });
      handleCloseCancelModal();
      loadPlanData(true); // Force refresh
    } catch (error) {
      console.error("Erro ao cancelar assinatura:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível cancelar a assinatura."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para reativar assinatura
  const handleReactivateSubscription = async () => {
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      await plansService.reactivateSubscription(companyId);
      toast_success({
        title: "Sucesso",
        message: "Assinatura reativada com sucesso."
      });
      loadPlanData();
    } catch (error) {
      console.error("Erro ao reativar assinatura:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível reativar a assinatura."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para formatar status
  const getStatusInfo = (status) => {
    switch (status) {
      case 'ACTIVE':
        return { 
          label: 'Ativo', 
          color: 'text-green-600 dark:text-green-400', 
          bgColor: 'bg-green-100 dark:bg-green-900/30',
          icon: CheckCircle 
        };
      case 'CANCELED':
        return { 
          label: 'Cancelado', 
          color: 'text-red-600 dark:text-red-400', 
          bgColor: 'bg-red-100 dark:bg-red-900/30',
          icon: XCircle 
        };
      case 'PAST_DUE':
        return { 
          label: 'Em Atraso', 
          color: 'text-yellow-600 dark:text-yellow-400', 
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
          icon: AlertCircle 
        };
      default:
        return { 
          label: status, 
          color: 'text-gray-600 dark:text-gray-400', 
          bgColor: 'bg-gray-100 dark:bg-gray-900/30',
          icon: AlertCircle 
        };
    }
  };

  // Função para formatar ciclo de cobrança
  const getBillingCycleLabel = (cycle) => {
    switch (cycle) {
      case 'MONTHLY':
        return 'Mensal';
      case 'YEARLY':
        return 'Anual';
      default:
        return cycle;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="animate-spin h-8 w-8 text-gray-400" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Carregando dados do plano...</span>
      </div>
    );
  }

  // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada
  if (isSystemAdmin && !selectedCompanyId) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Planos"
          icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
          description="Gerencie planos, usuários e módulos das assinaturas das empresas."
          moduleColor="admin"
          filters={
            <div className="w-full sm:w-64">
              <ModuleSelect
                moduleColor="admin"
                value={selectedCompanyId}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                placeholder="Selecione uma empresa"
                disabled={isLoadingCompanies}
              >
                <option value="">Selecione uma empresa</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          }
        />

        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            Selecione uma empresa
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.
          </p>
        </div>
      </div>
    );
  }

  if (!planData) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Planos"
          icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
          description="Gerencie seu plano, usuários e módulos da assinatura."
          moduleColor="admin"
          filters={
            isSystemAdmin && (
              <div className="w-full sm:w-64">
                <ModuleSelect
                  moduleColor="admin"
                  value={selectedCompanyId}
                  onChange={(e) => setSelectedCompanyId(e.target.value)}
                  placeholder="Selecione uma empresa"
                  disabled={isLoadingCompanies}
                >
                  <option value="">Selecione uma empresa</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>
            )
          }
        />

        <div className="text-center py-12">
          <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            Nenhum plano encontrado
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Não foi possível encontrar informações do plano para esta empresa.
          </p>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(planData.subscription.status);
  const StatusIcon = statusInfo.icon;

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <ModuleHeader
        title="Gerenciamento de Planos"
        icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        description={isSystemAdmin
          ? `Gerencie o plano, usuários e módulos da assinatura de ${planData.company.name}.`
          : "Gerencie seu plano, usuários e módulos da assinatura."
        }
        moduleColor="admin"
        filters={
          isSystemAdmin && (
            <div className="w-full sm:w-64">
              <ModuleSelect
                moduleColor="admin"
                value={selectedCompanyId}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                placeholder="Selecione uma empresa"
                disabled={isLoadingCompanies}
              >
                <option value="">Selecione uma empresa</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          )
        }
      />

      {/* Cards principais */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Card do Plano Atual */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <Crown className="mr-2 h-5 w-5 text-yellow-500" />
              Plano Atual
            </h2>
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color}`}>
              <StatusIcon className="mr-1 h-3 w-3" />
              {statusInfo.label}
            </div>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Empresa</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {planData.company.name}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Ciclo de Cobrança</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {getBillingCycleLabel(planData.subscription.billingCycle)}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Preço Mensal</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  R$ {planData.subscription.pricePerMonth.toFixed(2)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Próxima Cobrança</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {planData.subscription.nextBillingDate
                    ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR')
                    : 'N/A'
                  }
                </p>
              </div>
            </div>

            {/* Ações do plano */}
            <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
              {planData.subscription.status === 'ACTIVE' ? (
                <button
                  onClick={handleOpenCancelModal}
                  disabled={isUpdating}
                  className="flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
                >
                  <XCircle className="mr-1 h-4 w-4" />
                  Cancelar Plano
                </button>
              ) : (
                <button
                  onClick={handleReactivateSubscription}
                  disabled={isUpdating}
                  className="flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
                >
                  <CheckCircle className="mr-1 h-4 w-4" />
                  Reativar Plano
                </button>
              )}

              <button
                onClick={() => {
                  const url = isSystemAdmin && selectedCompanyId
                    ? `/subscription/invoices?companyId=${selectedCompanyId}`
                    : '/subscription/invoices';
                  router.push(url);
                }}
                className="flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors"
              >
                <Calendar className="mr-1 h-4 w-4" />
                Ver Faturas
              </button>
            </div>
          </div>
        </div>

        {/* Card de Uso de Usuários */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-4">
            <Users className="mr-2 h-5 w-5 text-blue-500" />
            Usuários
          </h3>

          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                <span>Uso atual</span>
                <span>{planData.usage.currentUsers} / {planData.usage.userLimit}</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(planData.usage.userLimitUsage, 100)}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {planData.usage.userLimitUsage}% utilizado
              </p>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {planData.usage.availableUsers} usuários disponíveis
              </p>
              <button
                onClick={handleOpenAddUsersModal}
                disabled={isUpdating}
                className="w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
              >
                <Plus className="mr-1 h-4 w-4" />
                Adicionar Usuários
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de Módulos */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6">
          <Package className="mr-2 h-5 w-5 text-purple-500" />
          Módulos da Assinatura
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Módulos Ativos */}
          {planData.modules.map((module) => (
            <div key={module.id} className="border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {getModuleName(module.moduleType)}
                  </span>
                </div>
                <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                  Ativo
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                R$ {module.pricePerMonth.toFixed(2)}/mês
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Adicionado em {new Date(module.addedAt).toLocaleDateString('pt-BR')}
              </p>

              {/* Botão para remover módulo (apenas módulos opcionais) */}
              {!isBasicModule(module.moduleType) && (
                <button
                  onClick={() => openRemoveModuleConfirmation(module.moduleType)}
                  disabled={isUpdating}
                  className="mt-3 w-full flex items-center justify-center px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-400 text-xs font-medium rounded transition-colors disabled:opacity-50"
                >
                  <Minus className="mr-1 h-3 w-3" />
                  Remover
                </button>
              )}
            </div>
          ))}

          {/* Informação sobre o plano básico */}
          <div className="border border-blue-200 dark:border-blue-700 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center mb-2">
              <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
              <span className="font-medium text-blue-900 dark:text-blue-100">
                Plano Básico Completo
              </span>
            </div>
            <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
              Seu plano já inclui todos os módulos essenciais para o funcionamento completo do sistema.
            </p>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Administração completa</li>
              <li>• Sistema de agendamento</li>
              <li>• Gerenciamento de pessoas</li>
              <li>• Relatórios e dashboards</li>
              <li>• Suporte técnico incluído</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Modal de Confirmação de Módulos */}
      {showModuleConfirmModal && selectedModule && (
        <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
          {/* Overlay de fundo escuro */}
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={closeModuleConfirmModal}></div>

          <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 z-[11050]">
            <div className="p-6">
              {/* Cabeçalho do Modal */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full mr-4 ${
                    moduleAction === 'add'
                      ? 'bg-blue-100 dark:bg-blue-900/30'
                      : 'bg-red-100 dark:bg-red-900/30'
                  }`}>
                    {moduleAction === 'add' ? (
                      <Plus className={`h-6 w-6 ${moduleAction === 'add' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400'}`} />
                    ) : (
                      <Minus className={`h-6 w-6 ${moduleAction === 'add' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400'}`} />
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {moduleAction === 'add' ? 'Adicionar Módulo' : 'Remover Módulo'}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {moduleAction === 'add' ? 'Esta ação afetará sua cobrança mensal' : 'Esta ação é irreversível'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={closeModuleConfirmModal}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* Conteúdo do Modal */}
              <div className="space-y-6">
                {/* Aviso Principal */}
                <div className={`border rounded-lg p-4 ${
                  moduleAction === 'add'
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                }`}>
                  <div className="flex items-start">
                    {moduleAction === 'add' ? (
                      <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0" />
                    )}
                    <div className={`text-sm ${
                      moduleAction === 'add'
                        ? 'text-blue-800 dark:text-blue-200'
                        : 'text-red-800 dark:text-red-200'
                    }`}>
                      <p className="font-semibold mb-2">
                        {moduleAction === 'add' ? '💰 ATENÇÃO: Impacto Financeiro' : '⚠️ ATENÇÃO: Consequências da Remoção'}
                      </p>
                      <ul className="space-y-1 list-disc list-inside">
                        {moduleAction === 'add' ? (
                          <>
                            <li>O valor será adicionado à sua mensalidade imediatamente</li>
                            <li>A cobrança será proporcional ao período restante do ciclo</li>
                            <li>Você terá acesso completo ao módulo após a confirmação</li>
                            <li>O módulo ficará ativo até o cancelamento manual</li>
                          </>
                        ) : (
                          <>
                            <li>Você perderá acesso a TODAS as funcionalidades do módulo</li>
                            <li>Os dados permanecerão salvos, mas inacessíveis</li>
                            <li>Sua equipe não conseguirá mais usar este módulo</li>
                            <li>Para reativar, será necessário adicionar novamente</li>
                          </>
                        )}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Informações do Módulo */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                    <Package className="h-4 w-4 mr-2" />
                    {moduleAction === 'add' ? 'Módulo a ser adicionado:' : 'Módulo a ser removido:'}
                  </h4>
                  <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                    <div className="flex justify-between">
                      <span>Nome:</span>
                      <span className="font-medium">{getModuleName(selectedModule.type)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Valor mensal:</span>
                      <span className="font-medium">
                        R$ {moduleAction === 'add'
                          ? selectedModule.info?.monthlyPrice?.toFixed(2) || '0.00'
                          : selectedModule.info?.pricePerMonth?.toFixed(2) || '0.00'
                        }
                      </span>
                    </div>
                    {moduleAction === 'add' && (
                      <div className="flex justify-between">
                        <span>Novo total mensal:</span>
                        <span className="font-medium text-blue-600 dark:text-blue-400">
                          R$ {(planData.subscription.pricePerMonth + (selectedModule.info?.monthlyPrice || 0)).toFixed(2)}
                        </span>
                      </div>
                    )}
                    {moduleAction === 'remove' && (
                      <div className="flex justify-between">
                        <span>Novo total mensal:</span>
                        <span className="font-medium text-green-600 dark:text-green-400">
                          R$ {(planData.subscription.pricePerMonth - (selectedModule.info?.pricePerMonth || 0)).toFixed(2)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Aviso Final */}
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                    <div className="text-sm text-yellow-800 dark:text-yellow-200">
                      <p className="font-medium mb-1">Confirmação necessária</p>
                      <p>
                        {moduleAction === 'add'
                          ? 'Tem certeza de que deseja adicionar este módulo? O valor será cobrado imediatamente.'
                          : 'Tem certeza de que deseja remover este módulo? Esta ação não pode ser desfeita facilmente.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Botões de Ação */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={closeModuleConfirmModal}
                  disabled={isUpdating}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50"
                >
                  Cancelar
                </button>
                <button
                  onClick={confirmModuleAction}
                  disabled={isUpdating}
                  className={`px-4 py-2 text-white rounded-md transition-colors disabled:opacity-50 flex items-center ${
                    moduleAction === 'add'
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {isUpdating ? (
                    <>
                      <RefreshCw className="animate-spin h-4 w-4 mr-2" />
                      Processando...
                    </>
                  ) : (
                    <>
                      {moduleAction === 'add' ? (
                        <Plus className="h-4 w-4 mr-2" />
                      ) : (
                        <Minus className="h-4 w-4 mr-2" />
                      )}
                      {moduleAction === 'add' ? 'Confirmar Adição' : 'Confirmar Remoção'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Cancelamento de Assinatura */}
      {showCancelModal && (
        <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
          {/* Overlay de fundo escuro */}
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={handleCloseCancelModal}></div>

          <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 z-[11050]">
            <div className="p-6">
              {/* Cabeçalho do Modal */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full mr-4">
                    <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Cancelar Assinatura
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Esta ação é irreversível
                    </p>
                  </div>
                </div>
                <button
                  onClick={handleCloseCancelModal}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* Conteúdo do Modal */}
              <div className="space-y-6">
                {/* Aviso Principal */}
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <div className="flex items-start">
                    <Ban className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0" />
                    <div className="text-sm text-red-800 dark:text-red-200">
                      <p className="font-semibold mb-2">⚠️ ATENÇÃO: Consequências do Cancelamento</p>
                      <ul className="space-y-1 list-disc list-inside">
                        <li>Você perderá acesso a TODOS os módulos do sistema</li>
                        <li>Todos os dados permanecerão salvos, mas inacessíveis</li>
                        <li>Sua equipe não conseguirá mais fazer login</li>
                        <li>Relatórios e funcionalidades ficarão bloqueados</li>
                        <li>Para reativar, será necessário contratar novamente</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Informações do Plano Atual */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                    <Lock className="h-4 w-4 mr-2" />
                    O que você está cancelando:
                  </h4>
                  <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                    <div className="flex justify-between">
                      <span>Empresa:</span>
                      <span className="font-medium">{planData.company.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Valor mensal:</span>
                      <span className="font-medium">R$ {planData.subscription.pricePerMonth.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Usuários ativos:</span>
                      <span className="font-medium">{planData.usage.currentUsers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Módulos ativos:</span>
                      <span className="font-medium">{planData.modules.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Próxima cobrança:</span>
                      <span className="font-medium">
                        {planData.subscription.nextBillingDate
                          ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR')
                          : 'N/A'
                        }
                      </span>
                    </div>
                  </div>
                </div>

                {/* Campo de Confirmação */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Para confirmar o cancelamento, digite <span className="font-bold text-red-600 dark:text-red-400">"CANCELAR ASSINATURA"</span> no campo abaixo:
                  </label>
                  <input
                    type="text"
                    value={cancelConfirmationText}
                    onChange={(e) => setCancelConfirmationText(e.target.value)}
                    placeholder="Digite: CANCELAR ASSINATURA"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  />
                </div>

                {/* Aviso Final */}
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                    <div className="text-sm text-yellow-800 dark:text-yellow-200">
                      <p className="font-medium mb-1">Última chance!</p>
                      <p>Tem certeza de que deseja cancelar? Esta ação não pode ser desfeita e você precisará contratar novamente para ter acesso ao sistema.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Botões de Ação */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={handleCloseCancelModal}
                  disabled={isUpdating}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50"
                >
                  Manter Assinatura
                </button>
                <button
                  onClick={handleCancelSubscription}
                  disabled={isUpdating || cancelConfirmationText !== 'CANCELAR ASSINATURA'}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center"
                >
                  {isUpdating ? (
                    <>
                      <RefreshCw className="animate-spin h-4 w-4 mr-2" />
                      Cancelando...
                    </>
                  ) : (
                    <>
                      <Ban className="h-4 w-4 mr-2" />
                      Confirmar Cancelamento
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Adicionar Usuários */}
      {showAddUsersModal && (
        <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
          {/* Overlay de fundo escuro */}
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={handleCloseAddUsersModal}></div>

          <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 z-[11050]">
            <div className="p-6">
              {/* Cabeçalho do Modal */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                  <Users className="mr-2 h-5 w-5 text-blue-500" />
                  Adicionar Usuários ao Plano
                </h3>
                <button
                  onClick={handleCloseAddUsersModal}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <XCircle className="h-5 w-5" />
                </button>
              </div>

              {/* Conteúdo do Modal */}
              <div className="space-y-4">
                {/* Informações do Plano Atual */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Plano Atual</h4>
                  <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <p>Usuários atuais: {planData.usage.currentUsers} / {planData.usage.userLimit}</p>
                    <p>Preço atual: R$ {planData.subscription.pricePerMonth.toFixed(2)}/mês</p>
                    <p>Preço por usuário: R$ {calculatePricePerUser().toFixed(2)}/mês</p>
                  </div>
                </div>

                {/* Seletor de Quantidade */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Quantidade de usuários a adicionar
                  </label>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => setAdditionalUsersCount(Math.max(1, additionalUsersCount - 1))}
                      className="flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
                    >
                      <Minus className="h-4 w-4" />
                    </button>
                    <input
                      type="number"
                      min="1"
                      max="100"
                      value={additionalUsersCount}
                      onChange={(e) => setAdditionalUsersCount(Math.max(1, parseInt(e.target.value) || 1))}
                      className="w-20 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />
                    <button
                      onClick={() => setAdditionalUsersCount(Math.min(100, additionalUsersCount + 1))}
                      className="flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Resumo do Custo */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Resumo da Alteração</h4>
                  <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    {(() => {
                      const costInfo = calculateAdditionalCost();
                      return (
                        <>
                          <p>Usuários adicionais: {additionalUsersCount}</p>
                          <p>Custo adicional: {formatCurrency(costInfo.additionalCost)}/mês</p>
                          <p className="font-semibold">
                            Novo total: {formatCurrency(costInfo.newPrice)}/mês
                          </p>
                          {costInfo.discountImprovement > 0 && (
                            <p className="text-green-600 dark:text-green-400 text-xs">
                              ✨ Desconto melhorou de {costInfo.newDiscount - costInfo.discountImprovement}% para {costInfo.newDiscount}%!
                            </p>
                          )}
                          <p className="text-xs text-blue-600 dark:text-blue-300 mt-2">
                            * A cobrança será proporcional ao período restante do ciclo atual
                          </p>
                        </>
                      );
                    })()}
                  </div>
                </div>

                {/* Aviso Importante */}
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                    <div className="text-sm text-yellow-800 dark:text-yellow-200">
                      <p className="font-medium mb-1">Atenção:</p>
                      <p>Esta ação irá aumentar o valor da sua assinatura mensalmente. A cobrança adicional será aplicada imediatamente de forma proporcional.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Botões de Ação */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={handleCloseAddUsersModal}
                  disabled={isUpdating}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleAddUsers}
                  disabled={isUpdating}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center"
                >
                  {isUpdating ? (
                    <>
                      <RefreshCw className="animate-spin h-4 w-4 mr-2" />
                      Processando...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Confirmar Adição
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Função auxiliar para obter nome do módulo
const getModuleName = (moduleType) => {
  const moduleNames = {
    'BASIC': 'Módulo Básico',
    'ADMIN': 'Administração',
    'SCHEDULING': 'Agendamento',
    'PEOPLE': 'Pessoas',
    'REPORTS': 'Relatórios',
    'CHAT': 'Chat',
    'ABAPLUS': 'ABA+'
  };
  return moduleNames[moduleType] || moduleType;
};

// Função auxiliar para verificar se é módulo básico
const isBasicModule = (moduleType) => {
  return ['BASIC', 'ADMIN', 'SCHEDULING'].includes(moduleType);
};

export default PlansPage;
