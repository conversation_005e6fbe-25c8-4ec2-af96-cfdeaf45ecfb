import React, { useState, useEffect } from "react";
import { X, Loader2, Building, ChevronDown, ChevronRight, Search, Shield, Info, FileText, Settings, Users, DollarSign, Calendar, CheckSquare, AlertCircle } from "lucide-react";
import { professionsService } from "@/app/modules/admin/services/professionsService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useToast } from "@/contexts/ToastContext";
import { useAuth } from "@/contexts/AuthContext";
import { PERMISSIONS_CONFIG, getAllPermissions } from "@/utils/permissionConfig";

const ProfessionGroupFormModal = ({ isOpen, onClose, group = null, onSuccess }) => {
  const { toast_success, toast_error } = useToast();
  const { user } = useAuth();
  const isSystemAdmin = user?.role === "SYSTEM_ADMIN";

  // Estado para controlar a tab ativa
  const [activeTab, setActiveTab] = useState("info");

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    companyId: "",
    active: true,
    defaultModules: ["BASIC"],
    defaultPermissions: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [expandedModules, setExpandedModules] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPermissions, setFilteredPermissions] = useState([]);

  // Função para mudar de tab
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };



  // Carregar empresas quando o modal é aberto (apenas para system admin)
  useEffect(() => {
    if (isOpen && isSystemAdmin) {
      loadCompanies();
    }
  }, [isOpen, isSystemAdmin]);

  // Carregar dados do grupo quando selecionado
  useEffect(() => {
    if (group) {
      setFormData({
        name: group.name || "",
        description: group.description || "",
        companyId: group.companyId || "",
        active: group.active !== undefined ? group.active : true,
        defaultModules: group.defaultModules || ["BASIC"],
        defaultPermissions: group.defaultPermissions || []
      });
    } else {
      setFormData({
        name: "",
        description: "",
        companyId: user?.companyId || "",
        active: true,
        defaultModules: ["BASIC"],
        defaultPermissions: []
      });
    }
  }, [group, user]);

  // Inicializar permissões filtradas quando o modal é aberto
  useEffect(() => {
    if (isOpen) {
      setFilteredPermissions(getAllPermissions());
    }
  }, [isOpen]);

  // Filtrar permissões com base no termo de busca
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredPermissions(getAllPermissions());
      return;
    }

    const searchTermLower = searchTerm.toLowerCase();
    const filtered = getAllPermissions().filter(
      (permission) =>
        permission.name.toLowerCase().includes(searchTermLower) ||
        permission.description.toLowerCase().includes(searchTermLower) ||
        permission.id.toLowerCase().includes(searchTermLower) ||
        PERMISSIONS_CONFIG[permission.moduleId].name
          .toLowerCase()
          .includes(searchTermLower)
    );

    setFilteredPermissions(filtered);

    // Expandir módulos que têm permissões correspondentes
    const modulesToExpand = {};
    filtered.forEach(permission => {
      modulesToExpand[permission.moduleId] = true;
    });

    setExpandedModules(prev => ({
      ...prev,
      ...modulesToExpand
    }));
  }, [searchTerm]);

  // Função para carregar empresas
  const loadCompanies = async () => {
    setIsLoadingCompanies(true);
    try {
      const companies = await companyService.getCompaniesForSelect();
      setCompanies(companies || []);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });
  };

  // Função para alternar um módulo
  const handleToggleModule = (moduleId) => {
    if (moduleId === "BASIC") return; // BASIC é obrigatório

    setFormData(prev => {
      const defaultModules = [...prev.defaultModules];

      if (defaultModules.includes(moduleId)) {
        return {
          ...prev,
          defaultModules: defaultModules.filter(m => m !== moduleId)
        };
      } else {
        return {
          ...prev,
          defaultModules: [...defaultModules, moduleId]
        };
      }
    });
  };

  // Função para alternar uma permissão
  const handleTogglePermission = (permissionId) => {
    setFormData(prev => {
      const defaultPermissions = [...prev.defaultPermissions];

      if (defaultPermissions.includes(permissionId)) {
        return {
          ...prev,
          defaultPermissions: defaultPermissions.filter(p => p !== permissionId)
        };
      } else {
        return {
          ...prev,
          defaultPermissions: [...defaultPermissions, permissionId]
        };
      }
    });
  };



  // Função para alternar a expansão de um módulo
  const toggleModuleExpansion = (moduleId) => {
    setExpandedModules(prev => ({
      ...prev,
      [moduleId]: !prev[moduleId]
    }));
  };

  // Alternar todas as permissões de um módulo
  const toggleModulePermissions = (moduleId) => {
    const moduleConfig = PERMISSIONS_CONFIG[moduleId];
    if (!moduleConfig) return;

    const permissions = moduleConfig.permissions;
    if (!permissions || permissions.length === 0) return;

    // Verificar se todas as permissões do módulo estão selecionadas
    const allSelected = permissions.every(p =>
      formData.defaultPermissions.includes(p.id)
    );

    // Se todas estiverem selecionadas, remover todas; caso contrário, adicionar todas
    if (allSelected) {
      // Remover todas as permissões deste módulo
      const updatedPermissions = formData.defaultPermissions.filter(
        id => !permissions.some(p => p.id === id)
      );
      setFormData(prev => ({
        ...prev,
        defaultPermissions: updatedPermissions
      }));
    } else {
      // Adicionar todas as permissões deste módulo que ainda não estão selecionadas
      const permissionsToAdd = permissions
        .filter(p => !formData.defaultPermissions.includes(p.id))
        .map(p => p.id);
      setFormData(prev => ({
        ...prev,
        defaultPermissions: [...prev.defaultPermissions, ...permissionsToAdd]
      }));
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Validar dados
      if (!formData.name.trim()) {
        toast_error("O nome do grupo é obrigatório");
        setIsSubmitting(false);
        return;
      }

      // Garantir que BASIC esteja sempre incluído nos módulos padrão
      if (!formData.defaultModules.includes("BASIC")) {
        formData.defaultModules.push("BASIC");
      }

      // Preparar dados para envio
      const dataToSend = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        active: formData.active,
        defaultModules: formData.defaultModules,
        defaultPermissions: formData.defaultPermissions
      };

      // Adicionar companyId apenas se for system admin
      if (isSystemAdmin) {
        dataToSend.companyId = formData.companyId || null;
      }

      if (group) {
        // Modo de edição
        await professionsService.updateProfessionGroup(group.id, dataToSend);
        toast_success("Grupo atualizado com sucesso");
      } else {
        // Modo de criação
        await professionsService.createProfessionGroup(dataToSend);
        toast_success("Grupo criado com sucesso");
      }

      if (onSuccess) onSuccess();
      onClose(); // Fechar o modal após salvar com sucesso
    } catch (error) {
      console.error("Erro ao salvar grupo:", error);
      toast_error(error.response?.data?.message || "Erro ao salvar grupo");
    } finally {
      setIsSubmitting(false);
    }
  };



  // Classes CSS comuns
  const inputClasses = "block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100";
  const iconContainerClasses = "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none";
  const labelClasses = "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1";

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-3xl w-full max-h-[90vh] flex flex-col z-[11050]">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-white">
            {group ? "Editar Grupo de Profissões" : "Novo Grupo de Profissões"}
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-neutral-200 dark:border-gray-700">
          <div className="flex">
            <button
              onClick={() => handleTabChange("info")}
              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "info"
                ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                : "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                }`}
            >
              <Info size={16} />
              <span>Informações</span>
            </button>
            <button
              onClick={() => handleTabChange("modules")}
              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "modules"
                ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                : "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                }`}
            >
              <Shield size={16} />
              <span>Módulos</span>
            </button>
            <button
              onClick={() => handleTabChange("permissions")}
              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "permissions"
                ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                : "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                }`}
            >
              <FileText size={16} />
              <span>Permissões</span>
            </button>
          </div>
        </div>

        {/* Form */}
        <div className="overflow-y-auto p-6">
          {/* Tab de Informações */}
          {activeTab === "info" && (
            <div>
              <div className="mb-6">
                <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1">
                  {group?.name || "Novo Grupo de Profissões"}
                </h4>
                <p className="text-sm text-neutral-600 dark:text-gray-400 mb-4">
                  Preencha as informações básicas do grupo de profissões:
                </p>
              </div>

              <div className="space-y-6">
                {/* Nome */}
                <div>
                  <label className={labelClasses} htmlFor="name">
                    Nome <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Info className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className={inputClasses}
                      required
                      placeholder="Nome do grupo de profissões"
                    />
                  </div>
                </div>

                {/* Descrição */}
                <div>
                  <label className={labelClasses} htmlFor="description">
                    Descrição
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <FileText className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      rows="3"
                      className={`${inputClasses} resize-none`}
                      placeholder="Descrição detalhada do grupo de profissões"
                    ></textarea>
                  </div>
                </div>

                {/* Empresa (apenas para system admin) */}
                {isSystemAdmin && (
                  <div>
                    <label className={labelClasses} htmlFor="companyId">
                      Empresa
                    </label>
                    <div className="relative">
                      <div className={iconContainerClasses}>
                        <Building className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                      </div>
                      <select
                        id="companyId"
                        name="companyId"
                        value={formData.companyId}
                        onChange={handleChange}
                        className={inputClasses}
                        disabled={isLoadingCompanies}
                      >
                        <option value="">Selecione uma empresa</option>
                        {companies.map((company) => (
                          <option key={company.id} value={company.id}>
                            {company.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    {isLoadingCompanies && (
                      <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1">
                        <Loader2 size={12} className="animate-spin" />
                        <span>Carregando empresas...</span>
                      </div>
                    )}
                  </div>
                )}

                {/* Status (apenas em modo de edição) */}
                {group && (
                  <div className="flex items-center mt-4">
                    <input
                      type="checkbox"
                      id="active"
                      name="active"
                      checked={formData.active}
                      onChange={handleChange}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label
                      htmlFor="active"
                      className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300"
                    >
                      Grupo ativo
                    </label>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Tab de Módulos */}
          {activeTab === "modules" && (
            <div>
              <div className="mb-6">
                <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1">
                  {group?.name || "Novo Grupo de Profissões"}
                </h4>
                <p className="text-sm text-neutral-600 dark:text-gray-400 mb-4">
                  Selecione os módulos padrão que serão atribuídos aos usuários deste grupo de profissões:
                </p>
              </div>

              <div className="space-y-4">
                {/* Módulo ADMIN */}
                <div
                  className={`p-4 rounded-lg border ${formData.defaultModules.includes("ADMIN") ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50" : "border-neutral-200 dark:border-gray-700"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}
                  onClick={() => handleToggleModule("ADMIN")}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <input
                        type="checkbox"
                        checked={formData.defaultModules.includes("ADMIN")}
                        onChange={() => {}}
                        disabled={isSubmitting}
                        className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Settings className={`h-5 w-5 ${formData.defaultModules.includes("ADMIN") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                        <h5 className="font-medium text-neutral-800 dark:text-white">Administração</h5>
                      </div>
                      <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                        Gerenciamento de usuários, empresas, configurações do sistema
                      </p>
                    </div>
                  </div>
                </div>

                {/* Módulo RH */}
                <div
                  className={`p-4 rounded-lg border ${formData.defaultModules.includes("RH") ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50" : "border-neutral-200 dark:border-gray-700"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}
                  onClick={() => handleToggleModule("RH")}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <input
                        type="checkbox"
                        checked={formData.defaultModules.includes("RH")}
                        onChange={() => {}}
                        disabled={isSubmitting}
                        className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Users className={`h-5 w-5 ${formData.defaultModules.includes("RH") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                        <h5 className="font-medium text-neutral-800 dark:text-white">Recursos Humanos</h5>
                      </div>
                      <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                        Gerenciamento de funcionários, folha de pagamento, benefícios
                      </p>
                    </div>
                  </div>
                </div>

                {/* Módulo FINANCIAL */}
                <div
                  className={`p-4 rounded-lg border ${formData.defaultModules.includes("FINANCIAL") ? "bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400 border-amber-200 dark:border-amber-800/50" : "border-neutral-200 dark:border-gray-700"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}
                  onClick={() => handleToggleModule("FINANCIAL")}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <input
                        type="checkbox"
                        checked={formData.defaultModules.includes("FINANCIAL")}
                        onChange={() => {}}
                        disabled={isSubmitting}
                        className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <DollarSign className={`h-5 w-5 ${formData.defaultModules.includes("FINANCIAL") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                        <h5 className="font-medium text-neutral-800 dark:text-white">Financeiro</h5>
                      </div>
                      <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                        Controle de faturas, pagamentos, despesas e relatórios financeiros
                      </p>
                    </div>
                  </div>
                </div>

                {/* Módulo SCHEDULING */}
                <div
                  className={`p-4 rounded-lg border ${formData.defaultModules.includes("SCHEDULING") ? "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50" : "border-neutral-200 dark:border-gray-700"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}
                  onClick={() => handleToggleModule("SCHEDULING")}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <input
                        type="checkbox"
                        checked={formData.defaultModules.includes("SCHEDULING")}
                        onChange={() => {}}
                        disabled={isSubmitting}
                        className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Calendar className={`h-5 w-5 ${formData.defaultModules.includes("SCHEDULING") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                        <h5 className="font-medium text-neutral-800 dark:text-white">Agendamento</h5>
                      </div>
                      <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                        Gerenciamento de compromissos, reuniões e alocação de recursos
                      </p>
                    </div>
                  </div>
                </div>

                {/* Módulo BASIC */}
                <div
                  className="p-4 rounded-lg border bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600 opacity-70"
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <input
                        type="checkbox"
                        checked={true}
                        onChange={() => {}}
                        disabled={true}
                        className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <CheckSquare className="h-5 w-5" />
                        <h5 className="font-medium text-neutral-800 dark:text-white">Básico</h5>
                      </div>
                      <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                        Acesso básico ao sistema, visualização limitada
                      </p>
                      <div className="mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1">
                        <AlertCircle size={12} />
                        <span>Módulo obrigatório para todos os usuários</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Tab de Permissões */}
          {activeTab === "permissions" && (
            <div>
              <div className="mb-6">
                <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1">
                  {group?.name || "Novo Grupo de Profissões"}
                </h4>
                <p className="text-sm text-neutral-600 dark:text-gray-400 mb-4">
                  Configure as permissões padrão que serão atribuídas aos usuários deste grupo de profissões:
                </p>

                <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg flex items-start gap-3 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50">
                  <div className="flex-shrink-0 mt-1">
                    <Info className="h-5 w-5 text-amber-500 dark:text-amber-400" />
                  </div>
                  <div>
                    <h5 className="font-medium text-amber-800 dark:text-amber-300">Importante</h5>
                    <p className="text-sm text-amber-700 dark:text-amber-400">
                      As permissões só serão aplicadas se o usuário também tiver
                      acesso ao módulo correspondente. Certifique-se de que o
                      usuário tenha os módulos necessários atribuídos.
                    </p>
                  </div>
                </div>
              </div>

              {/* Barra de pesquisa */}
              <div className="mb-6">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    type="text"
                    placeholder="Buscar permissões..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* Módulos Atribuídos */}
              <div className="bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg mb-6 dark:border dark:border-gray-700">
                <h4 className="font-medium text-neutral-700 dark:text-gray-300 mb-2">
                  Módulos Padrão
                </h4>
                <div className="flex flex-wrap gap-2">
                  {formData.defaultModules.map((moduleId) => (
                    <div
                      key={moduleId}
                      className="px-3 py-1.5 bg-white dark:bg-gray-700 border dark:border-gray-600 rounded-full flex items-center gap-2"
                    >
                      {moduleId === "ADMIN" && <Settings className="h-5 w-5" />}
                      {moduleId === "RH" && <Users className="h-5 w-5" />}
                      {moduleId === "FINANCIAL" && <DollarSign className="h-5 w-5" />}
                      {moduleId === "SCHEDULING" && <Calendar className="h-5 w-5" />}
                      {moduleId === "BASIC" && <CheckSquare className="h-5 w-5" />}
                      <span className="text-sm dark:text-gray-300">
                        {moduleId === "ADMIN" && "Administração"}
                        {moduleId === "RH" && "Recursos Humanos"}
                        {moduleId === "FINANCIAL" && "Financeiro"}
                        {moduleId === "SCHEDULING" && "Agendamento"}
                        {moduleId === "BASIC" && "Básico"}
                      </span>
                    </div>
                  ))}
                  {(!formData.defaultModules || formData.defaultModules.length === 0) && (
                    <p className="text-sm text-neutral-500 dark:text-gray-400">
                      Nenhum módulo atribuído
                    </p>
                  )}
                </div>
              </div>

              {/* Lista de permissões */}
              <div className="space-y-6">
                {/* Agrupar permissões por módulo */}
                {Object.entries(PERMISSIONS_CONFIG).map(([moduleId, moduleConfig]) => {
                  // Só mostrar módulos que o grupo tem acesso
                  if (!formData.defaultModules.includes(moduleId)) return null;

                  // Filtrar permissões deste módulo
                  const modulePermissions = filteredPermissions.filter(
                    (p) => p.moduleId === moduleId
                  );

                  if (modulePermissions.length === 0) return null;

                  return (
                    <div key={moduleId} className="mb-6 border rounded-lg overflow-hidden dark:border-gray-700">
                      {/* Cabeçalho do módulo */}
                      <div
                        className="bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between border-b dark:border-gray-700 cursor-pointer"
                        onClick={() => toggleModuleExpansion(moduleId)}
                      >
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-full bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400">
                            {moduleId === "ADMIN" && <Settings className="h-5 w-5" />}
                            {moduleId === "RH" && <Users className="h-5 w-5" />}
                            {moduleId === "FINANCIAL" && <DollarSign className="h-5 w-5" />}
                            {moduleId === "SCHEDULING" && <Calendar className="h-5 w-5" />}
                            {moduleId === "BASIC" && <CheckSquare className="h-5 w-5" />}
                          </div>
                          <div>
                            <h3 className="font-medium text-neutral-800 dark:text-gray-200">{moduleConfig.name}</h3>
                            <p className="text-sm text-neutral-500 dark:text-gray-400">
                              {moduleId === "ADMIN" && "Permissões para gerenciamento administrativo"}
                              {moduleId === "RH" && "Permissões para recursos humanos"}
                              {moduleId === "FINANCIAL" && "Permissões para gestão financeira"}
                              {moduleId === "SCHEDULING" && "Permissões para agendamento"}
                              {moduleId === "BASIC" && "Permissões básicas do sistema"}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <button
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleModulePermissions(moduleId);
                            }}
                            className="px-3 py-1 rounded text-sm font-medium bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700"
                          >
                            Selecionar todas
                          </button>
                          {expandedModules[moduleId] ? (
                            <ChevronDown className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                          )}
                        </div>
                      </div>

                      {/* Lista de permissões do módulo */}
                      {expandedModules[moduleId] && (
                        <div className="p-4 divide-y dark:divide-gray-700 dark:bg-gray-850">
                          {modulePermissions.map((permission) => (
                            <div key={permission.id} className="py-3 first:pt-0 last:pb-0">
                              <div className="flex items-start gap-3">
                                <div className="flex-shrink-0 mt-0.5">
                                  <input
                                    type="checkbox"
                                    id={permission.id}
                                    checked={formData.defaultPermissions.includes(permission.id)}
                                    onChange={() => handleTogglePermission(permission.id)}
                                    className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500"
                                  />
                                </div>

                                <div className="flex-1">
                                  <label
                                    htmlFor={permission.id}
                                    className="block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer"
                                  >
                                    {permission.name}
                                  </label>
                                  <p className="mt-1 text-sm text-neutral-600 dark:text-gray-400">
                                    {permission.description}
                                  </p>
                                  <div className="mt-1 text-xs text-neutral-500 dark:text-gray-500">
                                    ID: {permission.id}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}

                {/* Mensagem quando não há permissões */}
                {filteredPermissions.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-neutral-500 dark:text-gray-400">
                      Nenhuma permissão encontrada.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800">
          <div>
            {activeTab !== "info" && (
              <button
                type="button"
                onClick={() => {
                  // Definir a aba anterior com base na aba atual
                  const prevTab = {
                    modules: "info",
                    permissions: "modules"
                  }[activeTab];
                  setActiveTab(prevTab);
                }}
                className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
                disabled={isSubmitting}
              >
                Voltar
              </button>
            )}
          </div>
          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  {activeTab === "info" && <Info size={16} />}
                  {activeTab === "modules" && <Shield size={16} />}
                  {activeTab === "permissions" && <FileText size={16} />}
                  <span>
                    {!group ? (
                      (activeTab === "permissions") ? "Criar Grupo" : "Continuar"
                    ) : (
                      `Salvar ${activeTab === "info" ? "Grupo" :
                              activeTab === "modules" ? "Módulos" :
                              "Permissões"}`
                    )}
                  </span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfessionGroupFormModal;
