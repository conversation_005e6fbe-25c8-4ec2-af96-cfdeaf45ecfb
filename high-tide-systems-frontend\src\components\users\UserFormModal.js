"use client";

import React, { useState, useEffect, useRef } from "react";
import { X, Loader2, User, Mail, Lock, Calendar, Phone, MapPin, CreditCard, Building, Briefcase, Shield, UserCog, AlertCircle, DollarSign, Settings, Users, CheckSquare, Info, Search, ChevronDown, ChevronRight, FileText } from "lucide-react";
import UserProfileImageUpload from "../forms/UserProfileImageUpload";
import { userService } from "@/app/modules/admin/services/userService";
import companyService from "@/app/modules/admin/services/companyService";
import { branchService } from "@/app/modules/admin/services/branchService";
import { professionsService } from "@/app/modules/admin/services/professionsService";
import { useToast } from "@/contexts/ToastContext";
import { PERMISSIONS_CONFIG, getAllPermissions } from "@/utils/permissionConfig";
import { api } from "@/utils/api";
import AddressForm from "../common/AddressForm";
import MaskedInput from "../common/MaskedInput";
import UserDocumentsTab from "./UserDocumentsTab";
import { ModuleInput, ModuleSelect, ModuleFormGroup } from "@/components/ui";

const UserFormModal = ({ isOpen, onClose, user, onSuccess, currentUser }) => {
  const profileImageUploadRef = useRef(null);
  const { toast_success, toast_error, toast_warning } = useToast();
  const [activeTab, setActiveTab] = useState("info");
  const [savedUserId, setSavedUserId] = useState(null);
  const [formData, setFormData] = useState({
    login: "",
    email: "",
    fullName: "",
    password: "",
    confirmPassword: "",
    cpf: "",
    cnpj: "",
    documentType: "cpf",
    birthDate: "",
    address: "",
    neighborhood: "",
    city: "",
    state: "",
    postalCode: "",
    phone: "",
    companyId: "",
    branchId: "",
    professionId: ""
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [loadingCompanies, setLoadingCompanies] = useState(false);
  const [branches, setBranches] = useState([]);
  const [loadingBranches, setLoadingBranches] = useState(false);
  const [professions, setProfessions] = useState([]);
  const [loadingProfessions, setLoadingProfessions] = useState(false);
  const [selectedProfession, setSelectedProfession] = useState(null);
  const [professionDefaultModules, setProfessionDefaultModules] = useState([]);
  const [professionDefaultPermissions, setProfessionDefaultPermissions] = useState([]);
  const [isUsingProfessionDefaults, setIsUsingProfessionDefaults] = useState(true);

  // Estados para as tabs adicionais
  const [selectedModules, setSelectedModules] = useState([]);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [selectedRole, setSelectedRole] = useState("EMPLOYEE");

  // Estados para controlar quais abas estão disponíveis
  const [availableTabs, setAvailableTabs] = useState({
    info: true,
    documents: false,
    role: false,
    modules: false,
    permissions: false
  });

  // Estado para documentos temporários
  const [tempDocuments, setTempDocuments] = useState([]);

  // Estados específicos para a tab de permissões
  const [expandedModules, setExpandedModules] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPermissions, setFilteredPermissions] = useState([]);

  // Verificar se o usuário atual é um system_admin
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";
  const isAdmin = currentUser?.modules?.includes("ADMIN");

  useEffect(() => {
    // Carrega as empresas apenas se o usuário for system_admin
    if (isSystemAdmin && isOpen) {
      loadCompanies();
    }

    // Carrega as profissões quando o modal é aberto
    if (isOpen) {
      loadProfessions();
    }
  }, [isSystemAdmin, isOpen]);

  // Carregar unidades quando a empresa mudar
  useEffect(() => {
    if (formData.companyId) {
      loadBranches(formData.companyId);
    } else {
      setBranches([]);
    }
  }, [formData.companyId]);

  // Função para carregar unidades
  const loadBranches = async (companyId) => {
    if (!companyId) return;

    setLoadingBranches(true);
    try {
      const response = await branchService.getBranches({
        companyId,
        active: true,
        limit: 100
      });

      setBranches(response.branches || []);
    } catch (error) {
      console.error("Erro ao carregar unidades:", error);
      // Mostra um erro específico no formulário
      setErrors(prev => ({
        ...prev,
        branches: "Não foi possível carregar a lista de unidades"
      }));
      // Define unidades como array vazio para não quebrar o componente
      setBranches([]);
    } finally {
      setLoadingBranches(false);
    }
  };

  const loadCompanies = async () => {
    setLoadingCompanies(true);
    try {
      const companies = await companyService.getCompaniesForSelect();
      setCompanies(companies || []);

      // Se o usuário não tiver uma empresa selecionada e houver empresas disponíveis, seleciona a primeira
      if (isSystemAdmin && !formData.companyId && companies && companies.length > 0) {
        setFormData(prev => ({
          ...prev,
          companyId: companies[0].id
        }));
      }
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
      // Mostra um erro específico no formulário
      setErrors(prev => ({
        ...prev,
        companies: "Não foi possível carregar a lista de empresas"
      }));
      // Define empresas como array vazio para não quebrar o componente
      setCompanies([]);
    } finally {
      setLoadingCompanies(false);
    }
  };

  // Função para carregar profissões
  const loadProfessions = async () => {
    setLoadingProfessions(true);
    try {
      const data = await professionsService.getProfessions({ active: true });
      setProfessions(data || []);
    } catch (error) {
      console.error("Erro ao carregar profissões:", error);
      // Mostra um erro específico no formulário
      setErrors(prev => ({
        ...prev,
        professions: "Não foi possível carregar a lista de profissões"
      }));
      // Define profissões como array vazio para não quebrar o componente
      setProfessions([]);
    } finally {
      setLoadingProfessions(false);
    }
  };

  // Função para carregar detalhes da profissão selecionada
  const loadProfessionDetails = async (professionId) => {
    if (!professionId) {
      setSelectedProfession(null);
      setProfessionDefaultModules(["BASIC"]);
      setProfessionDefaultPermissions([]);
      return;
    }

    try {
      const profession = await professionsService.getProfessionById(professionId);
      setSelectedProfession(profession);

      if (profession?.group) {
        setProfessionDefaultModules(profession.group.defaultModules || ["BASIC"]);
        setProfessionDefaultPermissions(profession.group.defaultPermissions || []);

        // Se estiver usando padrões da profissão e não for edição de usuário existente
        if (isUsingProfessionDefaults && !user) {
          setSelectedModules(profession.group.defaultModules || ["BASIC"]);
          setSelectedPermissions(profession.group.defaultPermissions || []);
        }
      } else {
        setProfessionDefaultModules(["BASIC"]);
        setProfessionDefaultPermissions([]);
      }
    } catch (error) {
      console.error("Erro ao carregar detalhes da profissão:", error);
      setSelectedProfession(null);
      setProfessionDefaultModules(["BASIC"]);
      setProfessionDefaultPermissions([]);
    }
  };

  // Função para formatar CPF: converte "99999999999" para "999.999.999-99"
  const formatCpf = (value) => {
    if (!value) return "";

    // Remove qualquer caractere não numérico
    const digits = String(value).replace(/\D/g, "");

    // Limita a 11 dígitos
    const cleaned = digits.slice(0, 11);

    // Aplicar formatação conforme o tamanho do input
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `${cleaned.slice(0, 3)}.${cleaned.slice(3)}`;
    } else if (cleaned.length <= 9) {
      return `${cleaned.slice(0, 3)}.${cleaned.slice(3, 6)}.${cleaned.slice(6)}`;
    } else {
      return `${cleaned.slice(0, 3)}.${cleaned.slice(3, 6)}.${cleaned.slice(6, 9)}-${cleaned.slice(9, 11)}`;
    }
  };

  // Função para formatar CNPJ: converte "99999999999999" para "99.999.999/9999-99"
  const formatCnpj = (value) => {
    if (!value) return "";

    // Remove qualquer caractere não numérico
    const digits = String(value).replace(/\D/g, "");

    // Limita a 14 dígitos
    const cleaned = digits.slice(0, 14);

    // Aplicar formatação conforme o tamanho do input
    if (cleaned.length <= 2) {
      return cleaned;
    } else if (cleaned.length <= 5) {
      return `${cleaned.slice(0, 2)}.${cleaned.slice(2)}`;
    } else if (cleaned.length <= 8) {
      return `${cleaned.slice(0, 2)}.${cleaned.slice(2, 5)}.${cleaned.slice(5)}`;
    } else if (cleaned.length <= 12) {
      return `${cleaned.slice(0, 2)}.${cleaned.slice(2, 5)}.${cleaned.slice(5, 8)}/${cleaned.slice(8)}`;
    } else {
      return `${cleaned.slice(0, 2)}.${cleaned.slice(2, 5)}.${cleaned.slice(5, 8)}/${cleaned.slice(8, 12)}-${cleaned.slice(12, 14)}`;
    }
  };

  // Função para formatar telefone: converte "99999999999" para "(99) 99999-9999"
  const formatPhone = (value) => {
    if (!value) return "";

    // Remove qualquer caractere não numérico
    const digits = String(value).replace(/\D/g, "");

    // Limita a 11 dígitos
    const cleaned = digits.slice(0, 11);

    // Aplicar formatação conforme o tamanho do input
    if (cleaned.length <= 2) {
      return cleaned.length ? `(${cleaned}` : "";
    } else if (cleaned.length <= 7) {
      return `(${cleaned.slice(0, 2)}) ${cleaned.slice(2)}`;
    } else {
      return `(${cleaned.slice(0, 2)}) ${cleaned.slice(2, 7)}-${cleaned.slice(7)}`;
    }
  };

  useEffect(() => {
    if (user) {
      // Inicializa com valores básicos
      setFormData({
        login: user?.login || "",
        email: user?.email || "",
        fullName: user?.fullName || "",
        password: "", // Não preenchemos a senha para edição
        confirmPassword: "",
        cpf: user?.cpf ? formatCpf(user.cpf) : "",
        cnpj: user?.cnpj ? formatCnpj(user.cnpj) : "",
        documentType: user?.cpf ? "cpf" : "cnpj",
        birthDate: user?.birthDate ? new Date(user.birthDate).toISOString().split('T')[0] : "",
        address: user?.address || "",
        neighborhood: user?.neighborhood || "",
        city: user?.city || "",
        state: user?.state || "",
        postalCode: user?.postalCode || "",
        phone: user?.phone ? formatPhone(user.phone) : "",
        companyId: user?.companyId || "",
        branchId: user?.branchId || "",
        professionId: user?.professionObj?.id || "",
        profileImageUrl: user?.profileImageFullUrl || ""
      });

      // Inicializar os estados das tabs adicionais
      setSelectedModules(user.modules || ["BASIC"]);
      setSelectedPermissions(user.permissions || []);
      setSelectedRole(user.role || "EMPLOYEE");
      setSavedUserId(user.id);

      // Carregar detalhes da profissão se houver
      if (user?.professionObj?.id) {
        loadProfessionDetails(user.professionObj.id);
      }

      // Inicialmente, expandimos apenas os módulos que o usuário tem acesso
      const initialExpandedState = {};
      user.modules?.forEach((moduleId) => {
        initialExpandedState[moduleId] = true;
      });
      setExpandedModules(initialExpandedState);

      // Inicializar permissões filtradas
      setFilteredPermissions(getAllPermissions());

      // Para usuários existentes, todas as abas estão disponíveis
      setAvailableTabs({
        info: true,
        documents: true,
        role: true,
        modules: true,
        permissions: true
      });

      // Desativar o uso automático de permissões padrão para usuários existentes
      setIsUsingProfessionDefaults(false);
    } else {
      // Novo usuário, limpa o formulário e define o companyId padrão
      resetForm();
      // Se não for system_admin, define o companyId automaticamente
      if (!isSystemAdmin && currentUser?.companyId) {
        setFormData(prev => ({
          ...prev,
          companyId: currentUser.companyId
        }));
      }

      // Inicializar os estados das tabs adicionais para novo usuário
      setSelectedModules(["BASIC"]);
      setSelectedPermissions([]);
      setSelectedRole("EMPLOYEE");
      setSavedUserId(null);

      // Inicializar permissões filtradas
      setFilteredPermissions(getAllPermissions());
      setExpandedModules({ BASIC: true });

      // Para novos usuários, apenas a aba de informações está disponível inicialmente
      setAvailableTabs({
        info: true,
        documents: false,
        role: false,
        modules: false,
        permissions: false
      });

      // Ativar o uso automático de permissões padrão para novos usuários
      setIsUsingProfessionDefaults(true);
    }
  }, [user, isOpen, currentUser, isSystemAdmin]);

  // Filtragem de permissões baseada na busca
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredPermissions(getAllPermissions());
      return;
    }

    const lowerSearch = searchTerm.toLowerCase();
    const filtered = getAllPermissions().filter(
      (permission) =>
        permission.name.toLowerCase().includes(lowerSearch) ||
        permission.description.toLowerCase().includes(lowerSearch) ||
        permission.id.toLowerCase().includes(lowerSearch) ||
        PERMISSIONS_CONFIG[permission.moduleId].name
          .toLowerCase()
          .includes(lowerSearch)
    );

    setFilteredPermissions(filtered);

    // Expande automaticamente os módulos que têm permissões que correspondem à busca
    const modulesToExpand = {};
    filtered.forEach((permission) => {
      modulesToExpand[permission.moduleId] = true;
    });

    setExpandedModules((prev) => ({
      ...prev,
      ...modulesToExpand,
    }));
  }, [searchTerm]);

  // Função para alternar a expansão de um módulo
  const toggleModuleExpansion = (moduleId) => {
    setExpandedModules((prev) => ({
      ...prev,
      [moduleId]: !prev[moduleId],
    }));
  };

  // Função para alternar a seleção de uma permissão
  const togglePermission = (permissionId) => {
    setSelectedPermissions((prev) => {
      if (prev.includes(permissionId)) {
        return prev.filter((id) => id !== permissionId);
      } else {
        return [...prev, permissionId];
      }
    });
  };

  // Função para alternar todas as permissões de um módulo
  const toggleModulePermissions = (moduleId) => {
    const modulePermissions = PERMISSIONS_CONFIG[moduleId].permissions.map(
      (p) => p.id
    );

    // Verificar se todas as permissões do módulo já estão selecionadas
    const allSelected = modulePermissions.every((p) =>
      selectedPermissions.includes(p)
    );

    if (allSelected) {
      // Remover todas as permissões do módulo
      setSelectedPermissions((prev) =>
        prev.filter((p) => !modulePermissions.includes(p))
      );
    } else {
      // Adicionar todas as permissões do módulo
      setSelectedPermissions((prev) => {
        const newPermissions = [...prev];
        modulePermissions.forEach((p) => {
          if (!newPermissions.includes(p)) {
            newPermissions.push(p);
          }
        });
        return newPermissions;
      });
    }
  };

  const resetForm = () => {
    setFormData({
      login: "",
      email: "",
      fullName: "",
      password: "",
      confirmPassword: "",
      cpf: "",
      cnpj: "",
      documentType: "cpf",
      birthDate: "",
      address: "",
      neighborhood: "",
      city: "",
      state: "",
      postalCode: "",
      phone: "",
      companyId: "",
      branchId: "",
      professionId: "",
      profileImageUrl: ""
    });
    setErrors({});
    setSelectedModules(["BASIC"]);
    setSelectedPermissions([]);
    setSelectedRole("EMPLOYEE");
    setSavedUserId(null);
    setActiveTab("info");

    // Reiniciar as abas disponíveis
    setAvailableTabs({
      info: true,
      documents: false,
      role: false,
      modules: false,
      permissions: false
    });

    // Limpar documentos temporários
    setTempDocuments([]);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.login) newErrors.login = "Login é obrigatório";
    if (!formData.fullName) newErrors.fullName = "Nome completo é obrigatório";

    if (!formData.email) {
      newErrors.email = "Email é obrigatório";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }

    // Se for um novo usuário ou se estiver alterando a senha
    if (!user || formData.password) {
      if (!formData.password) {
        newErrors.password = "Senha é obrigatória";
      } else if (formData.password.length < 6) {
        newErrors.password = "Senha deve ter no mínimo 6 caracteres";
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Senhas não conferem";
      }
    }

    if (formData.documentType === "cpf" && formData.cpf) {
      const cleanCpf = formData.cpf.replace(/\D/g, "");
      if (cleanCpf.length !== 11) {
        newErrors.cpf = "CPF deve ter 11 dígitos";
      }
    }

    if (formData.documentType === "cnpj" && formData.cnpj) {
      const cleanCnpj = formData.cnpj.replace(/\D/g, "");
      if (cleanCnpj.length !== 14) {
        newErrors.cnpj = "CNPJ deve ter 14 dígitos";
      }
    }

    // Verificar se a empresa foi selecionada (apenas para system_admin)
    if (isSystemAdmin && !formData.companyId) {
      newErrors.companyId = "Empresa é obrigatória";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Aplicar formatação em tempo real para campos específicos
    if (name === "cpf") {
      setFormData(prev => ({ ...prev, cpf: formatCpf(value) }));
    } else if (name === "cnpj") {
      setFormData(prev => ({ ...prev, cnpj: formatCnpj(value) }));
    } else if (name === "phone") {
      setFormData(prev => ({ ...prev, phone: formatPhone(value) }));
    } else if (name === "professionId") {
      setFormData(prev => ({ ...prev, professionId: value }));
      // Carregar detalhes da profissão quando ela for alterada
      loadProfessionDetails(value);
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Limpa o erro quando o usuário começa a digitar
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleDocumentTypeChange = (e) => {
    const { value } = e.target;
    setFormData((prev) => ({
      ...prev,
      documentType: value,
      // Limpa o valor do outro tipo de documento
      cpf: value === "cpf" ? prev.cpf : "",
      cnpj: value === "cnpj" ? prev.cnpj : ""
    }));
  };

  const handleImageUploaded = (imageUrl) => {
    if (imageUrl) {
      setFormData(prev => ({
        ...prev,
        profileImageUrl: imageUrl
      }));
    }
  };

  const handleTabChange = (tab) => {
    // Limpar mensagens de erro ao mudar de aba
    setErrors({});

    // Verificar se a aba está disponível
    if (!availableTabs[tab]) {
      // Se a aba não estiver disponível, mostrar mensagem
      toast_warning({
        title: "Aba não disponível",
        message: "Complete a etapa atual antes de avançar para a próxima"
      });
      return;
    }

    setActiveTab(tab);
  };

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();

    if (activeTab === "info" && !validateForm()) return;

    setIsLoading(true);

    try {
      // Se estiver editando um usuário existente
      if (user || savedUserId) {
        // Se estiver editando um usuário e tiver uma imagem selecionada, fazer o upload primeiro
        if (profileImageUploadRef.current?.hasSelectedFile()) {
          try {
            console.log('Fazendo upload da imagem de perfil...');
            const imageResponse = await profileImageUploadRef.current.uploadSelectedImage();
            console.log('Upload concluído com sucesso:', imageResponse);

            if (imageResponse && imageResponse.fullImageUrl) {
              // Atualizar o formData com a nova URL da imagem
              setFormData(prev => ({
                ...prev,
                profileImageUrl: imageResponse.fullImageUrl
              }));
            }
          } catch (error) {
            console.error('Erro ao fazer upload da imagem:', error);
            toast_warning("Não foi possível fazer o upload da imagem de perfil, mas o usuário será salvo");
            // Continuar mesmo se o upload falhar
          }
        }

        const targetId = user?.id || savedUserId;

        if (activeTab === "info") {
          // Atualizar informações básicas
          const payload = {
            login: formData.login,
            email: formData.email,
            fullName: formData.fullName,
            ...(formData.password && { password: formData.password }),
            ...(formData.documentType === "cpf" && formData.cpf ? { cpf: formData.cpf.replace(/\D/g, "") } : {}),
            ...(formData.documentType === "cnpj" && formData.cnpj ? { cnpj: formData.cnpj.replace(/\D/g, "") } : {}),
            ...(formData.birthDate && { birthDate: formData.birthDate }),
            ...(formData.address && { address: formData.address }),
            ...(formData.neighborhood && { neighborhood: formData.neighborhood }),
            ...(formData.city && { city: formData.city }),
            ...(formData.state && { state: formData.state }),
            ...(formData.postalCode && { postalCode: formData.postalCode }),
            ...(formData.phone && { phone: formData.phone.replace(/\D/g, "") }),
            ...(formData.professionId && { professionId: formData.professionId }),
            // Se for system_admin, usa o companyId selecionado, senão usa o do usuário atual
            companyId: isSystemAdmin ? formData.companyId : currentUser?.companyId
          };

          await userService.update(targetId, payload);
          toast_success({
            title: "Usuário atualizado",
            message: "O usuário foi atualizado com sucesso"
          });
          onSuccess();
        } else if (activeTab === "modules") {
          // Salvar módulos
          // Garante que o módulo BASIC sempre esteja presente
          const modulesToSave = selectedModules.includes("BASIC")
            ? selectedModules
            : [...selectedModules, "BASIC"];

          await userService.updateModules(targetId, modulesToSave);
          toast_success({
            title: "Módulos atualizados",
            message: "Os módulos do usuário foram atualizados com sucesso"
          });
          onSuccess();
        } else if (activeTab === "permissions") {
          // Salvar permissões
          await userService.updatePermissions(targetId, selectedPermissions);
          toast_success({
            title: "Permissões atualizadas",
            message: "As permissões do usuário foram atualizadas com sucesso"
          });
          onSuccess();
        } else if (activeTab === "role") {
          // Salvar função
          await userService.updateRole(targetId, selectedRole);
          toast_success({
            title: "Função atualizada",
            message: "A função do usuário foi atualizada com sucesso"
          });
          onSuccess();
        }
      } else {
        // Criando um novo usuário - fluxo de etapas
        if (activeTab === "info") {
          // Validar informações básicas e avançar para a próxima etapa
          // Atualizar as abas disponíveis
          setAvailableTabs(prev => ({
            ...prev,
            role: true
          }));

          // Avançar para a próxima aba - Documentos
          setAvailableTabs(prev => ({
            ...prev,
            documents: true
          }));

          setActiveTab("documents");

          toast_success({
            title: "Informações válidas",
            message: "Informações básicas validadas com sucesso"
          });
        } else if (activeTab === "documents") {
          // Avançar para a próxima aba - Função
          setAvailableTabs(prev => ({
            ...prev,
            role: true
          }));

          setActiveTab("role");

          toast_success({
            title: "Documentos adicionados",
            message: tempDocuments.length > 0
              ? `${tempDocuments.length} documento(s) adicionado(s) com sucesso`
              : "Você pode continuar sem adicionar documentos"
          });
        } else if (activeTab === "role") {
          // Verificar se o usuário é system_admin ou company_admin
          if (selectedRole === "SYSTEM_ADMIN" || selectedRole === "COMPANY_ADMIN") {
            // Para system_admin e company_admin, criar o usuário diretamente
            const payload = {
              login: formData.login,
              email: formData.email,
              fullName: formData.fullName,
              password: formData.password,
              ...(formData.documentType === "cpf" && formData.cpf ? { cpf: formData.cpf.replace(/\D/g, "") } : {}),
              ...(formData.documentType === "cnpj" && formData.cnpj ? { cnpj: formData.cnpj.replace(/\D/g, "") } : {}),
              ...(formData.birthDate && { birthDate: formData.birthDate }),
              ...(formData.address && { address: formData.address }),
              ...(formData.neighborhood && { neighborhood: formData.neighborhood }),
              ...(formData.city && { city: formData.city }),
              ...(formData.state && { state: formData.state }),
              ...(formData.postalCode && { postalCode: formData.postalCode }),
              ...(formData.phone && { phone: formData.phone.replace(/\D/g, "") }),
              ...(formData.professionId && { professionId: formData.professionId }),
              companyId: isSystemAdmin ? formData.companyId : currentUser?.companyId,
              // Se for funcionário, forçar o papel para EMPLOYEE
              role: currentUser?.role === "EMPLOYEE" ? "EMPLOYEE" : selectedRole,
              // Módulos e permissões serão definidos automaticamente pelo backend
              // para SYSTEM_ADMIN e COMPANY_ADMIN
              modules: [],
              permissions: []
            };

            // Criar o usuário
            const response = await userService.create(payload);
            setSavedUserId(response.id);

            // Fazer upload dos documentos temporários, se houver
            if (tempDocuments.length > 0 && response.id) {
              try {
                const formData = new FormData();
                tempDocuments.forEach(doc => {
                  formData.append("documents", doc.file);
                });

                // Criar um array com os tipos de documentos
                const documentTypes = tempDocuments.map(doc => doc.type);
                formData.append("types", JSON.stringify(documentTypes));

                // Fazer o upload dos documentos
                await api.post(`/documents/upload?targetId=${response.id}&targetType=user`, formData, {
                  headers: {
                    "Content-Type": "multipart/form-data",
                  },
                });

                console.log('Documentos enviados com sucesso');
              } catch (docError) {
                console.error("Erro ao fazer upload dos documentos:", docError);
                toast_warning({
                  title: "Atenção",
                  message: "Usuário criado, mas houve um erro ao enviar os documentos."
                });
              }
            }

            toast_success({
              title: "Usuário criado",
              message: "O usuário administrador foi criado com sucesso"
            });

            // Fechar o modal após criar o usuário com sucesso
            onSuccess();
          } else {
            // Para employee, avançar para a tela de módulos
            setAvailableTabs(prev => ({
              ...prev,
              modules: true
            }));

            setActiveTab("modules");

            toast_success({
              title: "Função selecionada",
              message: "Função selecionada com sucesso"
            });
          }
        } else if (activeTab === "modules") {
          // Validar módulos e avançar para a próxima etapa
          setAvailableTabs(prev => ({
            ...prev,
            permissions: true
          }));

          setActiveTab("permissions");

          toast_success({
            title: "Módulos selecionados",
            message: "Módulos selecionados com sucesso"
          });
        } else if (activeTab === "permissions") {
          // Última etapa - criar o usuário com todas as informações
          // Criar o payload completo
          const payload = {
            login: formData.login,
            email: formData.email,
            fullName: formData.fullName,
            password: formData.password,
            ...(formData.documentType === "cpf" && formData.cpf ? { cpf: formData.cpf.replace(/\D/g, "") } : {}),
            ...(formData.documentType === "cnpj" && formData.cnpj ? { cnpj: formData.cnpj.replace(/\D/g, "") } : {}),
            ...(formData.birthDate && { birthDate: formData.birthDate }),
            ...(formData.address && { address: formData.address }),
            ...(formData.neighborhood && { neighborhood: formData.neighborhood }),
            ...(formData.city && { city: formData.city }),
            ...(formData.state && { state: formData.state }),
            ...(formData.postalCode && { postalCode: formData.postalCode }),
            ...(formData.phone && { phone: formData.phone.replace(/\D/g, "") }),
            ...(formData.professionId && { professionId: formData.professionId }),
            // Se for system_admin, usa o companyId selecionado, senão usa o do usuário atual
            companyId: isSystemAdmin ? formData.companyId : currentUser?.companyId,
            // Se for funcionário, forçar o papel para EMPLOYEE
            role: currentUser?.role === "EMPLOYEE" ? "EMPLOYEE" : selectedRole
          };

          // Garantir que o módulo BASIC sempre esteja presente
          const modulesToSave = selectedModules.includes("BASIC")
            ? selectedModules
            : [...selectedModules, "BASIC"];

          payload.modules = modulesToSave;
          payload.permissions = selectedPermissions;

          // Criar o usuário
          const response = await userService.create(payload);
          setSavedUserId(response.id);

          // Fazer upload dos documentos temporários, se houver
          if (tempDocuments.length > 0 && response.id) {
            try {
              const formData = new FormData();
              tempDocuments.forEach(doc => {
                formData.append("documents", doc.file);
              });

              // Criar um array com os tipos de documentos
              const documentTypes = tempDocuments.map(doc => doc.type);
              formData.append("types", JSON.stringify(documentTypes));

              // Fazer o upload dos documentos
              await api.post(`/documents/upload?targetId=${response.id}&targetType=user`, formData, {
                headers: {
                  "Content-Type": "multipart/form-data",
                },
              });

              console.log('Documentos enviados com sucesso');
            } catch (docError) {
              console.error("Erro ao fazer upload dos documentos:", docError);
              toast_warning({
                title: "Atenção",
                message: "Usuário criado, mas houve um erro ao enviar os documentos."
              });
            }
          }

          toast_success({
            title: "Usuário criado",
            message: "O usuário foi criado com sucesso"
          });

          // Fechar o modal após criar o usuário com sucesso
          onSuccess();
        }
      }
    } catch (error) {
      console.error("Erro ao salvar usuário:", error);
      const apiErrors = error.response?.data?.errors;
      const errorMessage = error.response?.data?.message || "Erro ao salvar usuário";

      // Mostrar toast de erro
      toast_error({
        title: "Erro ao salvar usuário",
        message: errorMessage
      });

      // Atualizar os erros no formulário para feedback visual
      if (apiErrors) {
        const formattedErrors = {};
        apiErrors.forEach(err => {
          formattedErrors[err.param] = err.msg;
        });
        setErrors(formattedErrors);
      } else {
        // Não mostrar o erro no modal, apenas no toast
        // Mas manter o objeto de erros para referência
        setErrors({});
      }
    } finally {
      setIsLoading(false);
    }
  };

  const inputClasses = "block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100";
  const iconContainerClasses = "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none";
  const labelClasses = "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1";
  const errorClasses = "mt-1 text-xs text-red-600 dark:text-red-400";

  if (!isOpen) return null;

  return (
      <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
        {/* Overlay de fundo escuro */}
        <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

        <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-3xl w-full max-h-[90vh] flex flex-col z-[11050]">
          {/* Header */}
          <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-white">
              {user ? "Editar Usuário" : "Novo Usuário"}
            </h3>
            <button
              onClick={onClose}
              className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
            >
              <X size={20} />
            </button>
          </div>

          {/* Tabs */}
          <div className="border-b border-neutral-200 dark:border-gray-700">
            <div className="flex">
              <button
                onClick={() => handleTabChange("info")}
                className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "info"
                  ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                  : "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                  }`}
              >
                <User size={16} />
                <span>Informações</span>
              </button>
              <button
                onClick={() => handleTabChange("documents")}
                className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "documents"
                  ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                  : availableTabs.documents
                    ? "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                    : "text-neutral-400 dark:text-gray-500 cursor-not-allowed"
                  }`}
              >
                <FileText size={16} />
                <span>Documentos</span>
              </button>
              <button
                onClick={() => handleTabChange("role")}
                className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "role"
                  ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                  : availableTabs.role
                    ? "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                    : "text-neutral-400 dark:text-gray-500 cursor-not-allowed"
                  }`}
              >
                <UserCog size={16} />
                <span>Função</span>
              </button>
              <button
                onClick={() => handleTabChange("modules")}
                className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "modules"
                  ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                  : availableTabs.modules
                    ? "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                    : "text-neutral-400 dark:text-gray-500 cursor-not-allowed"
                  }`}
              >
                <Shield size={16} />
                <span>Módulos</span>
              </button>
              <button
                onClick={() => handleTabChange("permissions")}
                className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "permissions"
                  ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                  : availableTabs.permissions
                    ? "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                    : "text-neutral-400 dark:text-gray-500 cursor-not-allowed"
                  }`}
              >
                <Lock size={16} />
                <span>Permissões</span>
              </button>
            </div>
          </div>

          {/* Form */}
          <div className="overflow-y-auto p-6">
            {activeTab === "documents" && (
              <UserDocumentsTab
                userId={savedUserId || user?.id}
                onClose={() => handleTabChange("info")}
                isCreating={!savedUserId && !user}
                onAddTempDocument={(doc) => {
                  console.log('Adicionando documento temporário:', doc);
                  setTempDocuments(prev => [...prev, doc]);
                }}
                tempDocuments={tempDocuments}
              />
            )}
            {activeTab === "info" && (
              <form onSubmit={handleSubmit}>
                {/* Imagem de perfil */}
                <div className="flex justify-center mb-6">
                  <UserProfileImageUpload
                    userId={user?.id || savedUserId}
                    initialImageUrl={formData.profileImageUrl}
                    onImageUploaded={handleImageUploaded}
                    deferUpload={!user?.id && !savedUserId} // Adiar upload se for novo usuário
                    uploadRef={profileImageUploadRef}
                    size="large"
                    disabled={isLoading}
                  />
                </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Login */}
                <div>
                  <label className={labelClasses} htmlFor="login">
                    Login *
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="login"
                      name="login"
                      type="text"
                      value={formData.login}
                      onChange={handleChange}
                      className={`${inputClasses} ${
                        errors.login ? "border-red-500 dark:border-red-700" : ""
                      }`}
                      placeholder="nome.sobrenome"
                      disabled={!!user || isLoading}
                    />
                  </div>
                  {errors.login && <p className={errorClasses}>{errors.login}</p>}
                </div>

                {/* Nome completo */}
                <div>
                  <label className={labelClasses} htmlFor="fullName">
                    Nome completo *
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="fullName"
                      name="fullName"
                      type="text"
                      value={formData.fullName}
                      onChange={handleChange}
                      className={`${inputClasses} ${
                        errors.fullName ? "border-red-500 dark:border-red-700" : ""
                      }`}
                      placeholder="Nome completo"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.fullName && <p className={errorClasses}>{errors.fullName}</p>}
                </div>

                {/* Email */}
                <div>
                  <label className={labelClasses} htmlFor="email">
                    Email *
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Mail className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      className={`${inputClasses} ${
                        errors.email ? "border-red-500 dark:border-red-700" : ""
                      }`}
                      placeholder="<EMAIL>"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.email && <p className={errorClasses}>{errors.email}</p>}
                </div>

                {/* Profissão */}
                <div>
                  <ModuleFormGroup
                    moduleColor="admin"
                    label="Profissão"
                    htmlFor="professionId"
                    icon={<Briefcase size={16} />}
                    error={!!errors.professions}
                    errorMessage={errors.professions}
                  >
                    <ModuleSelect
                      moduleColor="admin"
                      id="professionId"
                      name="professionId"
                      value={formData.professionId || ''}
                      onChange={handleChange}
                      disabled={isLoading || loadingProfessions}
                      placeholder="Selecione uma profissão"
                    >
                      <option value="">Selecione uma profissão</option>
                      {professions.map((profession) => (
                        <option key={profession.id} value={profession.id}>
                          {profession.name} {profession.group ? `(${profession.group.name})` : ''}
                        </option>
                      ))}
                    </ModuleSelect>
                    {loadingProfessions && (
                      <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                        Carregando profissões...
                      </p>
                    )}
                  </ModuleFormGroup>

                  {/* Informações sobre permissões padrão do grupo de profissão */}
                  {selectedProfession?.group && (
                    <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                      <div className="flex items-start">
                        <Info className="h-5 w-5 text-blue-500 dark:text-blue-400 mr-2 mt-0.5" />
                        <div>
                          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                            Permissões padrão do grupo de profissão
                          </h4>
                          <p className="text-xs text-blue-700 dark:text-blue-400 mt-1">
                            {isUsingProfessionDefaults
                              ? "Este usuário receberá automaticamente os módulos e permissões padrão definidos para o grupo"
                              : "Este grupo de profissão tem módulos e permissões padrão definidos"} "{selectedProfession.group.name}".
                          </p>

                          {/* Toggle para usar permissões padrão */}
                          <div className="mt-2 flex items-center">
                            <input
                              type="checkbox"
                              id="useDefaultPermissions"
                              checked={isUsingProfessionDefaults}
                              onChange={() => {
                                setIsUsingProfessionDefaults(!isUsingProfessionDefaults);
                                if (!isUsingProfessionDefaults) {
                                  // Se ativando, aplicar as permissões padrão
                                  setSelectedModules(professionDefaultModules);
                                  setSelectedPermissions(professionDefaultPermissions);
                                }
                              }}
                              className="h-4 w-4 rounded border-blue-300 dark:border-blue-600 text-blue-600 focus:ring-blue-500 dark:bg-blue-900"
                            />
                            <label htmlFor="useDefaultPermissions" className="ml-2 text-xs text-blue-700 dark:text-blue-400">
                              Usar permissões padrão do grupo
                            </label>
                          </div>

                          {professionDefaultModules.length > 0 && (
                            <div className="mt-2">
                              <p className="text-xs font-medium text-blue-800 dark:text-blue-300">Módulos padrão:</p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {professionDefaultModules.map(moduleId => (
                                  <span key={moduleId} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-300">
                                    {moduleId}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          {professionDefaultPermissions.length > 0 && (
                            <div className="mt-2">
                              <p className="text-xs font-medium text-blue-800 dark:text-blue-300">Permissões padrão: {professionDefaultPermissions.length}</p>
                              <button
                                type="button"
                                onClick={() => setActiveTab("permissions")}
                                className="text-xs text-blue-600 dark:text-blue-400 hover:underline mt-1"
                              >
                                Ver detalhes
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Empresa - Apenas para SYSTEM_ADMIN */}
                {isSystemAdmin && (
                  <div>
                    <ModuleFormGroup
                      moduleColor="admin"
                      label="Empresa *"
                      htmlFor="companyId"
                      icon={<Building size={16} />}
                      error={!!errors.companyId}
                      errorMessage={errors.companyId}
                    >
                      <ModuleSelect
                        moduleColor="admin"
                        id="companyId"
                        name="companyId"
                        value={formData.companyId || ''}
                        onChange={handleChange}
                        disabled={isLoading || loadingCompanies}
                        required
                        placeholder="Selecione uma empresa"
                        error={!!errors.companyId}
                      >
                        <option value="">Selecione uma empresa</option>
                        {companies.map((company) => (
                          <option key={company.id} value={company.id}>
                            {company.name} {company.tradingName ? `(${company.tradingName})` : ''}
                          </option>
                        ))}
                      </ModuleSelect>
                    </ModuleFormGroup>
                  </div>
                )}

                {/* Unidade */}
                <div>
                  <ModuleFormGroup
                    moduleColor="admin"
                    label="Unidade"
                    htmlFor="branchId"
                    icon={<MapPin size={16} />}
                    error={!!errors.branchId}
                    errorMessage={errors.branchId}
                  >
                    <ModuleSelect
                      moduleColor="admin"
                      id="branchId"
                      name="branchId"
                      value={formData.branchId || ''}
                      onChange={handleChange}
                      disabled={isLoading || loadingBranches || !formData.companyId}
                      placeholder="Selecione uma unidade"
                      error={!!errors.branchId}
                    >
                      <option value="">Selecione uma unidade</option>
                      {branches.map((branch) => (
                        <option key={branch.id} value={branch.id}>
                          {branch.name} {branch.code ? `(${branch.code})` : ''}
                        </option>
                      ))}
                    </ModuleSelect>
                    {formData.companyId && branches.length === 0 && !loadingBranches && (
                      <p className="mt-1 text-xs text-amber-600 dark:text-amber-400">
                        Nenhuma unidade encontrada para esta empresa
                      </p>
                    )}
                  </ModuleFormGroup>
                </div>

                {/* Tipo de documento */}
                <div>
                  <label className={labelClasses}>Tipo de Documento</label>
                  <div className="flex space-x-6 mt-2">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="radio_cpf"
                        name="documentType"
                        value="cpf"
                        checked={formData.documentType === "cpf"}
                        onChange={handleDocumentTypeChange}
                        className="h-4 w-4 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400 border-gray-300 dark:border-gray-600"
                        disabled={isLoading}
                      />
                      <label htmlFor="radio_cpf" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        CPF
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="radio_cnpj"
                        name="documentType"
                        value="cnpj"
                        checked={formData.documentType === "cnpj"}
                        onChange={handleDocumentTypeChange}
                        className="h-4 w-4 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400 border-gray-300 dark:border-gray-600"
                        disabled={isLoading}
                      />
                      <label htmlFor="radio_cnpj" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        CNPJ
                      </label>
                    </div>
                  </div>
                </div>

                {/* CPF ou CNPJ */}
                <div>
                  <label className={labelClasses} htmlFor={formData.documentType === "cpf" ? "input_cpf" : "input_cnpj"}>
                    {formData.documentType.toUpperCase()}
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <CreditCard className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    {formData.documentType === "cpf" ? (
                      <MaskedInput
                        type="cpf"
                        id="input_cpf"
                        name="cpf"
                        value={formData.cpf || ''}
                        onChange={handleChange}
                        className={`${inputClasses} ${
                          errors.cpf ? "border-red-500 dark:border-red-700" : ""
                        }`}
                        placeholder="000.000.000-00"
                        disabled={isLoading}
                      />
                    ) : (
                      <MaskedInput
                        type="cnpj"
                        id="input_cnpj"
                        name="cnpj"
                        value={formData.cnpj || ''}
                        onChange={handleChange}
                        className={`${inputClasses} ${
                          errors.cnpj ? "border-red-500 dark:border-red-700" : ""
                        }`}
                        placeholder="00.000.000/0000-00"
                        disabled={isLoading}
                      />
                    )}
                  </div>
                  {errors.cpf && formData.documentType === "cpf" && (
                    <p className={errorClasses}>{errors.cpf}</p>
                  )}
                  {errors.cnpj && formData.documentType === "cnpj" && (
                    <p className={errorClasses}>{errors.cnpj}</p>
                  )}
                </div>

                {/* Data de nascimento */}
                <div>
                  <label className={labelClasses} htmlFor="birthDate">
                    Data de Nascimento
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Calendar className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="birthDate"
                      name="birthDate"
                      type="date"
                      value={formData.birthDate || ''}
                      onChange={handleChange}
                      className={inputClasses}
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Telefone */}
                <div>
                  <label className={labelClasses} htmlFor="phone">
                    Telefone
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Phone className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <MaskedInput
                      type="phone"
                      id="phone"
                      name="phone"
                      value={formData.phone || ''}
                      onChange={handleChange}
                      className={inputClasses}
                      placeholder="(00) 00000-0000"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Endereço */}
                <div className="md:col-span-2">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Endereço</h3>
                  <AddressForm
                    formData={formData}
                    setFormData={setFormData}
                    errors={errors}
                    isLoading={isLoading}
                    classes={{
                      input: inputClasses,
                      label: labelClasses,
                      error: errorClasses,
                      iconContainer: iconContainerClasses
                    }}
                  />
                </div>

                {/* Senha */}
                <div>
                  <label className={labelClasses} htmlFor="password">
                    Senha {!user && "*"}
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Lock className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="password"
                      name="password"
                      type="password"
                      value={formData.password || ''}
                      onChange={handleChange}
                      className={`${inputClasses} ${
                        errors.password ? "border-red-500 dark:border-red-700" : ""
                      }`}
                      placeholder={user ? "••••••••" : "Senha"}
                      required={!user}
                      disabled={isLoading}
                    />
                  </div>
                  {user ? (
                    <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                      Deixe em branco para manter a senha atual
                    </p>
                  ) : (
                    <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                      Mínimo de 6 caracteres
                    </p>
                  )}
                  {errors.password && <p className={errorClasses}>{errors.password}</p>}
                </div>

                {/* Confirmar Senha */}
                <div>
                  <label className={labelClasses} htmlFor="confirmPassword">
                    Confirmar Senha {!user && "*"}
                  </label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Lock className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      value={formData.confirmPassword || ''}
                      onChange={handleChange}
                      className={`${inputClasses} ${
                        errors.confirmPassword ? "border-red-500 dark:border-red-700" : ""
                      }`}
                      placeholder="Confirme a senha"
                      required={!user}
                      disabled={isLoading}
                    />
                  </div>
                  {errors.confirmPassword && (
                    <p className={errorClasses}>{errors.confirmPassword}</p>
                  )}
                </div>
              </div>
            </form>
            )}

            {activeTab === "modules" && (
              <div>
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-neutral-800 dark:text-white mb-1">
                    {user?.fullName || (savedUserId ? "Novo Usuário" : "")}
                  </h4>
                  <p className="text-sm text-neutral-600 dark:text-gray-300">
                    Selecione os módulos que este usuário terá acesso:
                  </p>
                </div>

                <div className="space-y-4">
                  {/* Módulo ADMIN */}
                  <div
                    className={`p-4 rounded-lg border ${selectedModules.includes("ADMIN") ? "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800/50" : "border-neutral-200 dark:border-gray-700"} ${!isAdmin ? "opacity-70" : "cursor-pointer hover:border-primary-300 dark:hover:border-primary-700"}`}
                    onClick={() => {
                      if (isAdmin) {
                        if (selectedModules.includes("ADMIN")) {
                          setSelectedModules(prev => prev.filter(m => m !== "ADMIN"));
                        } else {
                          setSelectedModules(prev => [...prev, "ADMIN"]);
                        }
                      }
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <input
                          type="checkbox"
                          checked={selectedModules.includes("ADMIN")}
                          onChange={() => {}}
                          disabled={!isAdmin || isLoading}
                          className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Settings className={`h-5 w-5 ${selectedModules.includes("ADMIN") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                          <h5 className="font-medium text-neutral-800 dark:text-white">
                            Administração
                            <span className="ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded">
                              Acesso Administrativo
                            </span>
                          </h5>
                        </div>
                        <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                          Acesso completo ao sistema, incluindo configurações e gerenciamento de usuários
                        </p>

                        {!isAdmin && (
                          <div className="mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1">
                            <AlertCircle size={12} />
                            <span>Apenas administradores podem conceder este acesso</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Módulo RH */}
                  <div
                    className={`p-4 rounded-lg border ${selectedModules.includes("RH") ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50" : "border-neutral-200 dark:border-gray-700"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}
                    onClick={() => {
                      if (selectedModules.includes("RH")) {
                        setSelectedModules(prev => prev.filter(m => m !== "RH"));
                      } else {
                        setSelectedModules(prev => [...prev, "RH"]);
                      }
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <input
                          type="checkbox"
                          checked={selectedModules.includes("RH")}
                          onChange={() => {}}
                          disabled={isLoading}
                          className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Users className={`h-5 w-5 ${selectedModules.includes("RH") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                          <h5 className="font-medium text-neutral-800 dark:text-white">Recursos Humanos</h5>
                        </div>
                        <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                          Gerenciamento de funcionários, folha de pagamento e gestão de benefícios
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Módulo FINANCIAL */}
                  <div
                    className={`p-4 rounded-lg border ${selectedModules.includes("FINANCIAL") ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50" : "border-neutral-200 dark:border-gray-700"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}
                    onClick={() => {
                      if (selectedModules.includes("FINANCIAL")) {
                        setSelectedModules(prev => prev.filter(m => m !== "FINANCIAL"));
                      } else {
                        setSelectedModules(prev => [...prev, "FINANCIAL"]);
                      }
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <input
                          type="checkbox"
                          checked={selectedModules.includes("FINANCIAL")}
                          onChange={() => {}}
                          disabled={isLoading}
                          className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <DollarSign className={`h-5 w-5 ${selectedModules.includes("FINANCIAL") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                          <h5 className="font-medium text-neutral-800 dark:text-white">Financeiro</h5>
                        </div>
                        <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                          Controle de faturas, pagamentos, despesas e relatórios financeiros
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Módulo SCHEDULING */}
                  <div
                    className={`p-4 rounded-lg border ${selectedModules.includes("SCHEDULING") ? "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50" : "border-neutral-200 dark:border-gray-700"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}
                    onClick={() => {
                      if (selectedModules.includes("SCHEDULING")) {
                        setSelectedModules(prev => prev.filter(m => m !== "SCHEDULING"));
                      } else {
                        setSelectedModules(prev => [...prev, "SCHEDULING"]);
                      }
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <input
                          type="checkbox"
                          checked={selectedModules.includes("SCHEDULING")}
                          onChange={() => {}}
                          disabled={isLoading}
                          className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Calendar className={`h-5 w-5 ${selectedModules.includes("SCHEDULING") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                          <h5 className="font-medium text-neutral-800 dark:text-white">Agendamento</h5>
                        </div>
                        <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                          Gerenciamento de compromissos, reuniões e alocação de recursos
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Módulo BASIC */}
                  <div
                    className="p-4 rounded-lg border bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600 opacity-70"
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <input
                          type="checkbox"
                          checked={true}
                          onChange={() => {}}
                          disabled={true}
                          className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <CheckSquare className="h-5 w-5" />
                          <h5 className="font-medium text-neutral-800 dark:text-white">Básico</h5>
                        </div>
                        <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                          Acesso básico ao sistema, visualização limitada
                        </p>
                        <div className="mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1">
                          <AlertCircle size={12} />
                          <span>Módulo obrigatório para todos os usuários</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "permissions" && (
              <div>
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1">
                    {user?.fullName || (savedUserId ? "Novo Usuário" : "")}
                  </h4>
                  <p className="text-sm text-neutral-600 dark:text-gray-400 mb-4">
                    Configure as permissões específicas que este usuário terá acesso
                    dentro de cada módulo:
                  </p>

                  <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg flex items-start gap-3 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50">
                    <div className="flex-shrink-0 mt-1">
                      <Info className="h-5 w-5 text-amber-500 dark:text-amber-400" />
                    </div>
                    <div>
                      <h5 className="font-medium text-amber-800 dark:text-amber-300">Importante</h5>
                      <p className="text-sm text-amber-700 dark:text-amber-400">
                        As permissões só serão aplicadas se o usuário também tiver
                        acesso ao módulo correspondente. Certifique-se de que o
                        usuário tenha os módulos necessários atribuídos.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Barra de pesquisa */}
                <div className="mb-6">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      type="text"
                      placeholder="Buscar permissões..."
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                {/* Módulos Atribuídos */}
                <div className="bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg mb-6 dark:border dark:border-gray-700">
                  <h4 className="font-medium text-neutral-700 dark:text-gray-300 mb-2">
                    Módulos Atribuídos
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedModules.map((moduleId) => (
                      <div
                        key={moduleId}
                        className="px-3 py-1.5 bg-white dark:bg-gray-700 border dark:border-gray-600 rounded-full flex items-center gap-2"
                      >
                        {moduleId === "ADMIN" && <Settings className="h-5 w-5" />}
                        {moduleId === "RH" && <Users className="h-5 w-5" />}
                        {moduleId === "FINANCIAL" && <DollarSign className="h-5 w-5" />}
                        {moduleId === "SCHEDULING" && <Calendar className="h-5 w-5" />}
                        {moduleId === "BASIC" && <CheckSquare className="h-5 w-5" />}
                        <span className="text-sm dark:text-gray-300">
                          {moduleId === "ADMIN" && "Administração"}
                          {moduleId === "RH" && "Recursos Humanos"}
                          {moduleId === "FINANCIAL" && "Financeiro"}
                          {moduleId === "SCHEDULING" && "Agendamento"}
                          {moduleId === "BASIC" && "Básico"}
                        </span>
                      </div>
                    ))}
                    {(!selectedModules || selectedModules.length === 0) && (
                      <p className="text-sm text-neutral-500 dark:text-gray-400">
                        Nenhum módulo atribuído
                      </p>
                    )}
                  </div>
                </div>

                {/* Lista de permissões */}
                <div className="space-y-6">
                  {/* Agrupar permissões por módulo */}
                  {Object.entries(PERMISSIONS_CONFIG).map(([moduleId, moduleConfig]) => {
                    // Só mostrar módulos que o usuário tem acesso
                    if (!selectedModules.includes(moduleId)) return null;

                    // Filtrar permissões deste módulo
                    const modulePermissions = filteredPermissions.filter(
                      (p) => p.moduleId === moduleId
                    );

                    if (modulePermissions.length === 0) return null;

                    return (
                      <div key={moduleId} className="mb-6 border rounded-lg overflow-hidden dark:border-gray-700">
                        {/* Cabeçalho do módulo */}
                        <div
                          className="bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between border-b dark:border-gray-700 cursor-pointer"
                          onClick={() => toggleModuleExpansion(moduleId)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-full bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400">
                              {moduleId === "ADMIN" && <Settings className="h-5 w-5" />}
                              {moduleId === "RH" && <Users className="h-5 w-5" />}
                              {moduleId === "FINANCIAL" && <DollarSign className="h-5 w-5" />}
                              {moduleId === "SCHEDULING" && <Calendar className="h-5 w-5" />}
                              {moduleId === "BASIC" && <CheckSquare className="h-5 w-5" />}
                            </div>
                            <div>
                              <h3 className="font-medium text-neutral-800 dark:text-gray-200">{moduleConfig.name}</h3>
                              <p className="text-sm text-neutral-500 dark:text-gray-400">
                                {moduleId === "ADMIN" && "Permissões para gerenciamento administrativo"}
                                {moduleId === "RH" && "Permissões para recursos humanos"}
                                {moduleId === "FINANCIAL" && "Permissões para gestão financeira"}
                                {moduleId === "SCHEDULING" && "Permissões para agendamento"}
                                {moduleId === "BASIC" && "Permissões básicas do sistema"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleModulePermissions(moduleId);
                              }}
                              className="px-3 py-1 rounded text-sm font-medium bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700"
                            >
                              Selecionar todas
                            </button>
                            {expandedModules[moduleId] ? (
                              <ChevronDown className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                            ) : (
                              <ChevronRight className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                            )}
                          </div>
                        </div>

                        {/* Lista de permissões do módulo */}
                        {expandedModules[moduleId] && (
                          <div className="p-4 divide-y dark:divide-gray-700 dark:bg-gray-850">
                            {modulePermissions.map((permission) => (
                              <div key={permission.id} className="py-3 first:pt-0 last:pb-0">
                                <div className="flex items-start gap-3">
                                  <div className="flex-shrink-0 mt-0.5">
                                    <input
                                      type="checkbox"
                                      id={permission.id}
                                      checked={selectedPermissions.includes(permission.id)}
                                      onChange={() => togglePermission(permission.id)}
                                      className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500"
                                    />
                                  </div>

                                  <div className="flex-1">
                                    <label
                                      htmlFor={permission.id}
                                      className="block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer"
                                    >
                                      {permission.name}
                                    </label>
                                    <p className="mt-1 text-sm text-neutral-600 dark:text-gray-400">
                                      {permission.description}
                                    </p>
                                    <div className="mt-1 text-xs text-neutral-500 dark:text-gray-500">
                                      ID: {permission.id}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}

                  {/* Mensagem quando não há permissões */}
                  {filteredPermissions.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-neutral-500 dark:text-gray-400">
                        Nenhuma permissão encontrada.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === "role" && (
              <div>
                <div className="mb-6">
                  <h4 className="text-lg font-medium text-neutral-800 dark:text-white mb-1">
                    {user?.fullName || (savedUserId ? "Novo Usuário" : "")}
                  </h4>
                  <p className="text-sm text-neutral-600 dark:text-gray-300">
                    Selecione a função deste usuário no sistema:
                  </p>
                </div>

                <div className="space-y-4">
                  {/* Função SYSTEM_ADMIN */}
                  {isSystemAdmin && (
                    <div
                      className={`p-4 rounded-lg border ${selectedRole === "SYSTEM_ADMIN" ? "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800/50" : "border-neutral-200 dark:border-gray-700"} ${!isSystemAdmin ? "opacity-70 cursor-not-allowed" : "cursor-pointer hover:border-primary-300 dark:hover:border-primary-700"}`}
                      onClick={() => isSystemAdmin && setSelectedRole("SYSTEM_ADMIN")}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          <input
                            type="radio"
                            checked={selectedRole === "SYSTEM_ADMIN"}
                            onChange={() => isSystemAdmin && setSelectedRole("SYSTEM_ADMIN")}
                            disabled={isLoading || !isSystemAdmin}
                            className="h-5 w-5 rounded-full border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                          />
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <UserCog className="h-5 w-5 text-red-600 dark:text-red-400" />
                            <h5 className="font-medium text-neutral-800 dark:text-white">
                              Administrador do Sistema
                              <span className="ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded">
                                Acesso Total
                              </span>
                            </h5>
                          </div>
                          <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                            Acesso completo a todas as funcionalidades e empresas
                          </p>
                          {!isSystemAdmin && (
                            <div className="mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1">
                              <AlertCircle size={12} />
                              <span>Apenas administradores do sistema podem conceder este acesso</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Função COMPANY_ADMIN */}
                  <div
                    className={`p-4 rounded-lg border ${selectedRole === "COMPANY_ADMIN" ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50" : "border-neutral-200 dark:border-gray-700"} ${currentUser?.role === "EMPLOYEE" ? "opacity-70 cursor-not-allowed" : "cursor-pointer hover:border-primary-300 dark:hover:border-primary-700"}`}
                    onClick={() => currentUser?.role !== "EMPLOYEE" && setSelectedRole("COMPANY_ADMIN")}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <input
                          type="radio"
                          checked={selectedRole === "COMPANY_ADMIN"}
                          onChange={() => currentUser?.role !== "EMPLOYEE" && setSelectedRole("COMPANY_ADMIN")}
                          disabled={isLoading || currentUser?.role === "EMPLOYEE"}
                          className={`h-5 w-5 rounded-full border-gray-300 dark:border-gray-600 ${currentUser?.role === "EMPLOYEE" ? 'opacity-50 cursor-not-allowed' : 'text-primary-500 dark:text-primary-400'} focus:ring-primary-500 dark:focus:ring-primary-400`}
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          <h5 className="font-medium text-neutral-800 dark:text-white">Administrador da Empresa</h5>
                        </div>
                        <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                          Acesso completo às funcionalidades dentro da empresa
                        </p>
                        {currentUser?.role === "EMPLOYEE" && (
                          <div className="mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1">
                            <AlertCircle size={12} />
                            <span>Como funcionário, você só pode criar outros funcionários</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Função EMPLOYEE */}
                  <div
                    className={`p-4 rounded-lg border ${selectedRole === "EMPLOYEE" ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50" : "border-neutral-200 dark:border-gray-700"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}
                    onClick={() => setSelectedRole("EMPLOYEE")}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <input
                          type="radio"
                          checked={selectedRole === "EMPLOYEE"}
                          onChange={() => setSelectedRole("EMPLOYEE")}
                          disabled={isLoading}
                          className="h-5 w-5 rounded-full border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                        />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <UserCog className="h-5 w-5 text-green-600 dark:text-green-400" />
                          <h5 className="font-medium text-neutral-800 dark:text-white">Funcionário</h5>
                        </div>
                        <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                          Acesso limitado às funcionalidades atribuídas
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Alerta de restrições para não administradores do sistema */}
                  {!isSystemAdmin && (
                    <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-lg">
                      <div className="flex items-start gap-2">
                        <AlertCircle className="h-5 w-5 text-amber-500 dark:text-amber-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <h5 className="font-medium text-amber-800 dark:text-amber-300">Restrições de acesso</h5>
                          <p className="text-sm text-amber-700 dark:text-amber-400">
                            {currentUser?.role === "EMPLOYEE" ?
                              "Como funcionário, você só pode criar outros funcionários. A opção de Administrador de Empresa está desabilitada." :
                              "Você tem permissões limitadas para alterar funções de usuários. Algumas opções podem não estar disponíveis."}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800">
            <div>
              {activeTab !== "info" && (
                <button
                  type="button"
                  onClick={() => {
                    // Definir a aba anterior com base na aba atual
                    const prevTab = {
                      role: "info",
                      modules: selectedRole === "EMPLOYEE" ? "role" : "info",
                      permissions: "modules"
                    }[activeTab];
                    setActiveTab(prevTab);
                  }}
                  className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
                  disabled={isLoading}
                >
                  Voltar
                </button>
              )}
            </div>
            <div className="flex gap-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
                disabled={isLoading}
              >
                Cancelar
              </button>
              <button
                type="button"
                onClick={handleSubmit}
                className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    <span>Salvando...</span>
                  </>
                ) : (
                  <>
                    {activeTab === "info" && <User size={16} />}
                    {activeTab === "modules" && <Shield size={16} />}
                    {activeTab === "permissions" && <Lock size={16} />}
                    {activeTab === "role" && <UserCog size={16} />}
                    <span>
                      {(!user && !savedUserId) ? (
                        (activeTab === "permissions" ||
                         (activeTab === "role" && (selectedRole === "SYSTEM_ADMIN" || selectedRole === "COMPANY_ADMIN"))) ?
                          "Criar Usuário" : "Continuar"
                      ) : (
                        `Salvar ${activeTab === "info" ? "Usuário" :
                                activeTab === "modules" ? "Módulos" :
                                activeTab === "permissions" ? "Permissões" : "Função"}`
                      )}
                    </span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
  );
};

export default UserFormModal;