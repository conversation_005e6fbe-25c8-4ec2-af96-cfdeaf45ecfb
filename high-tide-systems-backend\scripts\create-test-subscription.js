const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestSubscription() {
  console.log('Criando assinatura de teste...');

  try {
    // Buscar uma empresa para criar a assinatura
    const company = await prisma.company.findFirst({
      where: {
        NOT: {
          id: '00000000-0000-0000-0000-000000000001' // Excluir empresa de teste
        }
      }
    });

    if (!company) {
      console.log('Nenhuma empresa encontrada para criar assinatura');
      return;
    }

    console.log(`Criando assinatura para a empresa: ${company.name} (ID: ${company.id})`);

    // Verificar se já existe assinatura
    const existingSubscription = await prisma.subscription.findUnique({
      where: { companyId: company.id }
    });

    let subscription;

    if (existingSubscription) {
      console.log('Empresa já possui assinatura. Atualizando...');

      // Atualizar assinatura existente
      subscription = await prisma.subscription.update({
        where: { companyId: company.id },
        data: {
          status: 'ACTIVE',
          billingCycle: 'MONTHLY',
          pricePerMonth: 199.90,
          startDate: new Date(),
          nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
          active: true,
          cancelAtPeriodEnd: false
        },
        include: {
          modules: true
        }
      });

      console.log('Assinatura atualizada:', subscription);
    } else {
      // Criar nova assinatura
      subscription = await prisma.subscription.create({
        data: {
          companyId: company.id,
          status: 'ACTIVE',
          billingCycle: 'MONTHLY',
          pricePerMonth: 199.90,
          startDate: new Date(),
          nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
          active: true,
          cancelAtPeriodEnd: false,
          modules: {
            create: [
              {
                moduleType: 'BASIC',
                active: true,
                pricePerMonth: 0
              },
              {
                moduleType: 'ADMIN',
                active: true,
                pricePerMonth: 0
              },
              {
                moduleType: 'SCHEDULING',
                active: true,
                pricePerMonth: 49.90
              },
              {
                moduleType: 'RH',
                active: true,
                pricePerMonth: 39.90
              },
              {
                moduleType: 'FINANCIAL',
                active: true,
                pricePerMonth: 59.90
              }
            ]
          }
        },
        include: {
          modules: true
        }
      });

      console.log('Assinatura criada:', subscription);
    }

    // Criar algumas faturas de exemplo
    const invoices = await Promise.all([
      // Fatura paga do mês passado
      prisma.invoice.create({
        data: {
          subscriptionId: subscription.id,
          companyId: company.id,
          amount: 199.90,
          status: 'PAID',
          dueDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000),
          stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_1'
        }
      }),
      // Fatura paga de 2 meses atrás
      prisma.invoice.create({
        data: {
          subscriptionId: subscription.id,
          companyId: company.id,
          amount: 199.90,
          status: 'PAID',
          dueDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 65 * 24 * 60 * 60 * 1000),
          stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_2'
        }
      }),
      // Fatura pendente do mês atual
      prisma.invoice.create({
        data: {
          subscriptionId: subscription.id,
          companyId: company.id,
          amount: 199.90,
          status: 'PENDING',
          dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
          createdAt: new Date(),
          stripeInvoiceUrl: 'https://invoice.stripe.com/i/acct_test_123/test_invoice_3'
        }
      })
    ]);

    console.log(`Criadas ${invoices.length} faturas de exemplo`);

    console.log('\n✅ Assinatura de teste criada com sucesso!');
    console.log(`Empresa: ${company.name}`);
    console.log(`ID da empresa: ${company.id}`);
    console.log('Você pode agora testar a tela de planos selecionando esta empresa.');

  } catch (error) {
    console.error('Erro ao criar assinatura de teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestSubscription();
