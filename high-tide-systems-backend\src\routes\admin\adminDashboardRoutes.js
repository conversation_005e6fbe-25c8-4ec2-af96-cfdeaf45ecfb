// src/routes/dashboardRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { cacheMiddleware } = require('../../middlewares/cache');
const { AdminDashboardController } = require('../../controllers/adminDashboardController');

// Configurar TTL para cache do dashboard (5 minutos)
const DASHBOARD_CACHE_TTL = 300;

// Todas as rotas requerem autenticação
router.use(authenticate);

// Endpoint para obter todos os dados do dashboard de uma vez
router.get('/all', cacheMiddleware('dashboard:all', DASHBOARD_CACHE_TTL), AdminDashboardController.getAllDashboardData);

// Endpoints individuais para cada tipo de dado
router.get('/stats', cacheMiddleware('dashboard:stats', DASHBOARD_CACHE_TTL), AdminDashboardController.getStats);
router.get('/activity', cacheMiddleware('dashboard:activity', DASHBOARD_CACHE_TTL), AdminDashboardController.getActivityData);
router.get('/module-distribution', cacheMiddleware('dashboard:modules', DASHBOARD_CACHE_TTL), AdminDashboardController.getUserModuleDistribution);
router.get('/active-users', cacheMiddleware('dashboard:users', DASHBOARD_CACHE_TTL), AdminDashboardController.getActiveUsers);
router.get('/recent-activity', cacheMiddleware('dashboard:recent', DASHBOARD_CACHE_TTL), AdminDashboardController.getRecentActivity);
router.get('/profession-distribution', cacheMiddleware('dashboard:professions', DASHBOARD_CACHE_TTL), AdminDashboardController.getProfessionDistribution);
router.get('/system-info', cacheMiddleware('dashboard:system', DASHBOARD_CACHE_TTL), AdminDashboardController.getSystemInfo);
router.get('/plans', cacheMiddleware('dashboard:plans', DASHBOARD_CACHE_TTL), AdminDashboardController.getPlansData);

module.exports = router;