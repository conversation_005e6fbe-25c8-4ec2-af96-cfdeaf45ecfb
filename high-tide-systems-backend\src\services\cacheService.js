// src/services/cacheService.js
let redis;
try {
  redis = require('redis');
} catch (error) {
  console.warn('Módulo Redis não encontrado, usando mock em memória');
  // Mock do Redis para desenvolvimento
  redis = {
    createClient: () => ({
      connect: async () => {},
      quit: async () => {},
      on: (event, callback) => {},
      set: async () => 'OK',
      get: async () => null,
      del: async () => 1,
      scan: async () => ({ cursor: 0, keys: [] })
    })
  };
}

class CacheService {
  constructor() {
    this.client = null;
    this.connected = false;
    this.defaultTTL = 3600; // 1 hora em segundos
  }

  /**
   * Inicializa a conexão com o Redis
   */
  async initialize() {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

      this.client = redis.createClient({
        url: redisUrl
      });

      // Configurar handlers de eventos
      this.client.on('error', (err) => {
        console.error('Erro no Redis:', err);
        this.connected = false;
      });

      this.client.on('connect', () => {
        console.log('Conectado ao Redis');
        this.connected = true;
      });

      this.client.on('reconnecting', () => {
        console.log('Reconectando ao Redis...');
      });

      // Conectar ao Redis
      await this.client.connect();

      return { success: true };
    } catch (error) {
      console.error('Erro ao inicializar o serviço de cache:', error);
      this.connected = false;
      return { success: false, error: error.message };
    }
  }

  /**
   * Fecha a conexão com o Redis
   */
  async close() {
    if (this.client) {
      await this.client.quit();
      this.connected = false;
      console.log('Conexão com Redis fechada');
    }
  }

  /**
   * Gera uma chave de cache baseada nos parâmetros
   * @param {string} prefix - Prefixo da chave (geralmente o nome do recurso)
   * @param {Object} params - Parâmetros para gerar a chave
   * @returns {string} - Chave formatada
   */
  generateKey(prefix, params = {}) {
    // Converter parâmetros em string ordenada para garantir consistência
    const paramsStr = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null)
      .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
      .map(([key, value]) => `${key}:${value}`)
      .join('|');

    return `${prefix}:${paramsStr || 'default'}`;
  }

  /**
   * Armazena um valor no cache
   * @param {string} key - Chave do cache
   * @param {any} value - Valor a ser armazenado
   * @param {number} ttl - Tempo de vida em segundos (opcional)
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async set(key, value, ttl = this.defaultTTL) {
    if (!this.connected || !this.client) return false;

    try {
      // Converter valor para JSON
      const valueStr = JSON.stringify(value);

      // Armazenar no Redis com TTL
      await this.client.set(key, valueStr, { EX: ttl });

      return true;
    } catch (error) {
      console.error(`Erro ao armazenar no cache (${key}):`, error);
      return false;
    }
  }

  /**
   * Recupera um valor do cache
   * @param {string} key - Chave do cache
   * @returns {Promise<any>} - Valor armazenado ou null se não encontrado
   */
  async get(key) {
    if (!this.connected || !this.client) return null;

    try {
      const value = await this.client.get(key);

      if (!value) return null;

      // Converter de volta para objeto
      return JSON.parse(value);
    } catch (error) {
      console.error(`Erro ao recuperar do cache (${key}):`, error);
      return null;
    }
  }

  /**
   * Remove um valor do cache
   * @param {string} key - Chave do cache
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async delete(key) {
    if (!this.connected || !this.client) return false;

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error(`Erro ao remover do cache (${key}):`, error);
      return false;
    }
  }

  /**
   * Obtém o TTL (tempo de vida restante) de uma chave
   * @param {string} key - Chave do cache
   * @returns {Promise<number>} - TTL em segundos (-1 se não tem TTL, -2 se não existe)
   */
  async getTTL(key) {
    if (!this.connected || !this.client) return -2;

    try {
      return await this.client.ttl(key);
    } catch (error) {
      console.error(`Erro ao obter TTL do cache (${key}):`, error);
      return -2;
    }
  }

  /**
   * Limpa o cache com base em um padrão de chave
   * @param {string} pattern - Padrão de chave (ex: "users:*")
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async clear(pattern) {
    if (!this.connected || !this.client) return false;

    try {
      // Usar SCAN para encontrar chaves que correspondem ao padrão
      let cursor = 0;
      do {
        const result = await this.client.scan(cursor, {
          MATCH: pattern,
          COUNT: 100
        });

        cursor = result.cursor;

        // Se houver chaves, excluí-las
        if (result.keys.length > 0) {
          await this.client.del(result.keys);
        }
      } while (cursor !== 0);

      return true;
    } catch (error) {
      console.error(`Erro ao limpar cache (${pattern}):`, error);
      return false;
    }
  }

  /**
   * Recupera um valor do cache ou executa a função para obtê-lo
   * @param {string} key - Chave do cache
   * @param {Function} fn - Função para obter o valor se não estiver em cache
   * @param {number} ttl - Tempo de vida em segundos (opcional)
   * @returns {Promise<any>} - Valor do cache ou da função
   */
  async getOrSet(key, fn, ttl = this.defaultTTL) {
    // Tentar obter do cache primeiro
    const cachedValue = await this.get(key);

    if (cachedValue !== null) {
      return cachedValue;
    }

    try {
      // Executar função para obter o valor
      const value = await fn();

      // Armazenar no cache se o valor não for null ou undefined
      if (value !== null && value !== undefined) {
        await this.set(key, value, ttl);
      }

      return value;
    } catch (error) {
      console.error(`Erro ao executar função para cache (${key}):`, error);
      throw error;
    }
  }

  /**
   * Cache com fallback - tenta buscar no cache, se não encontrar executa a função
   * e armazena o resultado no cache
   * @param {string} key - Chave do cache
   * @param {Function} fallbackFn - Função para executar se não estiver em cache
   * @param {number} ttl - Tempo de vida em segundos
   * @param {Object} options - Opções adicionais
   * @returns {Promise<any>} - Valor do cache ou resultado da função
   */
  async getOrSet(key, fallbackFn, ttl = this.defaultTTL, options = {}) {
    try {
      // Tentar buscar no cache primeiro
      const cachedValue = await this.get(key);

      if (cachedValue !== null) {
        if (options.logHit) {
          console.log(`[CACHE] Hit: ${key}`);
        }
        return cachedValue;
      }

      // Se não estiver em cache, executar função
      if (options.logMiss) {
        console.log(`[CACHE] Miss: ${key}`);
      }

      const value = await fallbackFn();

      // Armazenar no cache se o valor for válido
      if (value !== null && value !== undefined) {
        await this.set(key, value, ttl);
      }

      return value;
    } catch (error) {
      console.error(`Erro no getOrSet para chave ${key}:`, error);
      // Em caso de erro, executar a função diretamente
      return await fallbackFn();
    }
  }

  /**
   * Invalidação inteligente de cache por tags
   * @param {string|Array} tags - Tag(s) para invalidar
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async invalidateByTags(tags) {
    if (!this.connected || !this.client) return false;

    try {
      const tagsArray = Array.isArray(tags) ? tags : [tags];

      for (const tag of tagsArray) {
        await this.clear(`*:${tag}:*`);
        await this.clear(`${tag}:*`);
      }

      return true;
    } catch (error) {
      console.error(`Erro ao invalidar cache por tags:`, error);
      return false;
    }
  }

  /**
   * Cache com compressão para dados grandes
   * @param {string} key - Chave do cache
   * @param {any} value - Valor a ser armazenado
   * @param {number} ttl - Tempo de vida em segundos
   * @param {boolean} compress - Se deve comprimir os dados
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async setCompressed(key, value, ttl = this.defaultTTL, compress = true) {
    if (!this.connected || !this.client) return false;

    try {
      let valueStr = JSON.stringify(value);

      // Comprimir se o valor for grande (> 1KB) e compressão estiver habilitada
      if (compress && valueStr.length > 1024) {
        const zlib = require('zlib');
        const compressed = zlib.gzipSync(valueStr);
        await this.client.set(`${key}:compressed`, compressed.toString('base64'), { EX: ttl });
      } else {
        await this.client.set(key, valueStr, { EX: ttl });
      }

      return true;
    } catch (error) {
      console.error(`Erro ao armazenar cache comprimido (${key}):`, error);
      return false;
    }
  }

  /**
   * Buscar cache com descompressão
   * @param {string} key - Chave do cache
   * @returns {Promise<any>} - Valor do cache ou null
   */
  async getCompressed(key) {
    if (!this.connected || !this.client) return null;

    try {
      // Tentar buscar versão comprimida primeiro
      const compressedValue = await this.client.get(`${key}:compressed`);

      if (compressedValue) {
        const zlib = require('zlib');
        const decompressed = zlib.gunzipSync(Buffer.from(compressedValue, 'base64'));
        return JSON.parse(decompressed.toString());
      }

      // Se não houver versão comprimida, buscar normal
      return await this.get(key);
    } catch (error) {
      console.error(`Erro ao buscar cache comprimido (${key}):`, error);
      return null;
    }
  }

  /**
   * Cache em lote - armazenar múltiplas chaves de uma vez
   * @param {Object} keyValuePairs - Objeto com chave-valor
   * @param {number} ttl - Tempo de vida em segundos
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async setBatch(keyValuePairs, ttl = this.defaultTTL) {
    if (!this.connected || !this.client) return false;

    try {
      const pipeline = this.client.multi();

      for (const [key, value] of Object.entries(keyValuePairs)) {
        const valueStr = JSON.stringify(value);
        pipeline.set(key, valueStr, { EX: ttl });
      }

      await pipeline.exec();
      return true;
    } catch (error) {
      console.error(`Erro ao armazenar cache em lote:`, error);
      return false;
    }
  }

  /**
   * Buscar múltiplas chaves de uma vez
   * @param {Array} keys - Array de chaves
   * @returns {Promise<Object>} - Objeto com resultados
   */
  async getBatch(keys) {
    if (!this.connected || !this.client) return {};

    try {
      const values = await this.client.mGet(keys);
      const result = {};

      keys.forEach((key, index) => {
        if (values[index]) {
          try {
            result[key] = JSON.parse(values[index]);
          } catch (error) {
            console.error(`Erro ao parsear valor do cache para chave ${key}:`, error);
            result[key] = null;
          }
        } else {
          result[key] = null;
        }
      });

      return result;
    } catch (error) {
      console.error(`Erro ao buscar cache em lote:`, error);
      return {};
    }
  }

  /**
   * Estatísticas do cache
   * @returns {Promise<Object>} - Estatísticas
   */
  async getStats() {
    if (!this.connected || !this.client) return null;

    try {
      const info = await this.client.info('memory');
      const stats = await this.client.info('stats');
      const keyspace = await this.client.info('keyspace');

      return {
        memory: this.parseRedisInfo(info),
        stats: this.parseRedisInfo(stats),
        keyspace: this.parseRedisInfo(keyspace),
        connected: this.connected
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas do cache:', error);
      return null;
    }
  }

  /**
   * Parser para informações do Redis
   * @param {string} infoString - String de informações do Redis
   * @returns {Object} - Objeto parseado
   */
  parseRedisInfo(infoString) {
    const result = {};
    const lines = infoString.split('\r\n');

    for (const line of lines) {
      if (line && !line.startsWith('#')) {
        const [key, value] = line.split(':');
        if (key && value) {
          result[key] = isNaN(value) ? value : Number(value);
        }
      }
    }

    return result;
  }

  /**
   * Retorna o cliente Redis para uso em outros serviços
   * @returns {Object} - Cliente Redis
   */
  getClient() {
    return this.client;
  }
}

// Exportar instância única
module.exports = new CacheService();
