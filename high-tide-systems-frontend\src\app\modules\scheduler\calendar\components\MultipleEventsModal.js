"use client";

import React, { useState } from 'react';
import { X, Edit, Trash2 } from 'lucide-react';
import { APPOINTMENT_STATUS } from '../utils/appointmentConstants';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import ConfirmationDialog from '@/components/ui/ConfirmationDialog';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import { useToast } from '@/contexts/ToastContext';

const MultipleEventsModal = ({
  isOpen,
  onClose,
  events,
  date,
  isDarkMode,
  onEditAppointment, // Função para abrir o modal de edição
  onAppointmentChange // Função para recarregar os agendamentos após alterações
}) => {
  // Estados para o diálogo de confirmação
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [appointmentToDelete, setAppointmentToDelete] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Hook de toast
  const { toast_success, toast_error } = useToast();

  if (!isOpen) return null;

  // Verificar se a data é válida
  const dateObj = date ? new Date(date) : new Date();

  // Função para abrir o modal de edição
  const handleEdit = (event) => {
    if (onEditAppointment) {
      onClose(); // Fechar o modal atual
      onEditAppointment(event); // Abrir o modal de edição
    }
  };

  // Função para confirmar a exclusão
  const handleDelete = (event) => {
    setAppointmentToDelete(event);
    setConfirmDialogOpen(true);
  };

  // Função para executar a exclusão
  const confirmDelete = async () => {
    if (!appointmentToDelete) return;

    setIsLoading(true);
    try {
      await appointmentService.deleteAppointment(appointmentToDelete.id);
      toast_success({
        title: "Sucesso",
        message: "Agendamento excluído com sucesso."
      });

      // Recarregar os agendamentos
      if (onAppointmentChange) {
        await onAppointmentChange();
      }

      // Fechar o modal se não houver mais agendamentos
      if (events.length <= 1) {
        onClose();
      }
    } catch (error) {
      console.error("Erro ao excluir agendamento:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível excluir o agendamento. Tente novamente."
      });
    } finally {
      setIsLoading(false);
      setConfirmDialogOpen(false);
      setAppointmentToDelete(null);
    }
  };

  // Formatar a data para exibição
  const formattedDate = format(dateObj, "dd 'de' MMMM 'de' yyyy", { locale: ptBR });
  const formattedTime = format(dateObj, "HH:mm", { locale: ptBR });

  // Agrupar eventos por horário
  const eventsByTime = events.reduce((acc, event) => {
    // Garantir que a data é válida
    const eventDate = event.start ? new Date(event.start) : new Date();
    const startTime = format(eventDate, 'HH:mm');

    if (!acc[startTime]) {
      acc[startTime] = [];
    }
    acc[startTime].push(event);
    return acc;
  }, {});

  // Ordenar eventos por horário
  const sortedTimeSlots = Object.keys(eventsByTime).sort();

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* Modal */}
      <div className={`relative w-full max-w-3xl max-h-[80vh] overflow-auto rounded-lg shadow-lg ${isDarkMode ? 'bg-neutral-800' : 'bg-white'}`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-4 border-b ${isDarkMode ? 'border-neutral-700' : 'border-neutral-200'}`}>
          <div>
            <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-neutral-800'}`}>
              Agendamentos do dia {formattedDate}
            </h2>
            <p className={`text-sm mt-1 ${isDarkMode ? 'text-neutral-400' : 'text-neutral-500'}`}>
              Horário: {formattedTime}
            </p>
          </div>
          <button
            onClick={onClose}
            className={`p-1 rounded-full hover:bg-opacity-10 ${isDarkMode ? 'hover:bg-white' : 'hover:bg-neutral-200'}`}
          >
            <X size={20} className={isDarkMode ? 'text-white' : 'text-neutral-600'} />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {events.length === 0 ? (
            <div className={`text-center py-8 ${isDarkMode ? 'text-neutral-400' : 'text-neutral-500'}`}>
              Nenhum agendamento encontrado para este horário.
            </div>
          ) : (
            <div>
              <div className={`mb-4 p-3 rounded-lg ${isDarkMode ? 'bg-neutral-700' : 'bg-neutral-100'}`}>
                <p className={`text-sm ${isDarkMode ? 'text-neutral-300' : 'text-neutral-600'}`}>
                  <strong>Total de agendamentos:</strong> {events.length}
                </p>
              </div>

              {sortedTimeSlots.map((time) => (
                <div key={time} className="mb-6">
                  <div className="flex items-center mb-2">
                    <div className={`w-1.5 h-1.5 rounded-full mr-2 bg-indigo-500`}></div>
                    <h3 className={`text-md font-semibold ${isDarkMode ? 'text-white' : 'text-neutral-800'}`}>
                      {time}
                    </h3>
                    <span className={`ml-2 text-xs px-2 py-0.5 rounded-full ${isDarkMode ? 'bg-neutral-700 text-neutral-300' : 'bg-neutral-200 text-neutral-700'}`}>
                      {eventsByTime[time].length} {eventsByTime[time].length === 1 ? 'agendamento' : 'agendamentos'}
                    </span>
                  </div>

                  <div className="space-y-3">
                    {eventsByTime[time].map(event => {
                      const status = event.extendedProps.status || "PENDING";
                      const statusConfig = APPOINTMENT_STATUS[status];

                      return (
                        <div
                          key={event.id}
                          className={`p-4 rounded-lg border ${isDarkMode ? 'border-neutral-700 bg-neutral-700/50' : 'border-neutral-200 bg-white'} hover:shadow-md transition-shadow duration-200`}
                          style={{
                            borderLeftWidth: '4px',
                            borderLeftColor: event.backgroundColor || '#6366f1'
                          }}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="space-y-3">
                                {/* Seção de alta prioridade - Paciente e Profissional */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 pb-2 border-b border-neutral-700/30">
                                  {/* Prioridade 1: Paciente */}
                                  <div>
                                    <p className={`text-xs uppercase tracking-wider ${isDarkMode ? 'text-neutral-400' : 'text-neutral-500'}`}>
                                      Paciente
                                    </p>
                                    <p className={`text-base font-medium ${isDarkMode ? 'text-white' : 'text-neutral-800'}`}>
                                      {event.extendedProps.personfullName}
                                    </p>
                                  </div>

                                  {/* Prioridade 2: Profissional */}
                                  <div>
                                    <p className={`text-xs uppercase tracking-wider ${isDarkMode ? 'text-neutral-400' : 'text-neutral-500'}`}>
                                      Profissional
                                    </p>
                                    <p className={`text-base font-medium ${isDarkMode ? 'text-white' : 'text-neutral-800'}`}>
                                      {event.extendedProps.providerfullName}
                                    </p>
                                  </div>
                                </div>

                                {/* Seção de média prioridade - Horário e Status */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 pt-1">
                                  {/* Prioridade 3: Horário */}
                                  <div className="flex items-center">
                                    <span className={`text-sm font-medium ${isDarkMode ? 'text-neutral-300' : 'text-neutral-600'} mr-2`}>Horário:</span>
                                    <span className={`text-sm ${isDarkMode ? 'text-neutral-200' : 'text-neutral-700'}`}>
                                      {event.start && event.end
                                        ? `${format(new Date(event.start), 'HH:mm')} - ${format(new Date(event.end), 'HH:mm')}`
                                        : 'Horário não definido'
                                      }
                                    </span>
                                  </div>

                                  {/* Prioridade 4: Status */}
                                  <div className="flex items-center">
                                    <span className={`text-sm font-medium ${isDarkMode ? 'text-neutral-300' : 'text-neutral-600'} mr-2`}>Status:</span>
                                    <span
                                      className={`px-2 py-0.5 text-xs rounded-full ${isDarkMode ? 'bg-opacity-20 bg-white' : 'bg-opacity-20'}`}
                                      style={{ backgroundColor: statusConfig.color + '33', color: isDarkMode ? statusConfig.darkColor : statusConfig.color }}
                                    >
                                      {statusConfig.label}
                                    </span>
                                  </div>

                                  {/* Prioridade 5: Serviço */}
                                  <div className="flex items-center">
                                    <span className={`text-sm font-medium ${isDarkMode ? 'text-neutral-300' : 'text-neutral-600'} mr-2`}>Serviço:</span>
                                    <span className={`text-sm ${isDarkMode ? 'text-neutral-200' : 'text-neutral-700'}`}>
                                      {event.extendedProps.serviceTypefullName}
                                    </span>
                                  </div>

                                  {/* Prioridade 6: Título */}
                                  <div className="flex items-center">
                                    <span className={`text-sm font-medium ${isDarkMode ? 'text-neutral-300' : 'text-neutral-600'} mr-2`}>Título:</span>
                                    <span className={`text-sm ${isDarkMode ? 'text-neutral-200' : 'text-neutral-700'}`}>
                                      {event.title}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {event.extendedProps.description && (
                                <div className={`mt-4 pt-3 border-t ${isDarkMode ? 'border-neutral-700/30' : 'border-neutral-200'}`}>
                                  <p className={`text-xs uppercase tracking-wider mb-1 ${isDarkMode ? 'text-neutral-400' : 'text-neutral-500'}`}>
                                    Observações
                                  </p>
                                  <p className={`text-sm ${isDarkMode ? 'text-neutral-300' : 'text-neutral-600'}`}>
                                    {event.extendedProps.description}
                                  </p>
                                </div>
                              )}
                            </div>

                            {/* Botões de ação */}
                            <div className="flex space-x-2 mt-2">
                              <button
                                onClick={() => handleEdit(event)}
                                className={`p-2 rounded-full transition-all ${
                                  isDarkMode
                                    ? 'text-blue-400 hover:bg-blue-900/30 hover:shadow-inner'
                                    : 'text-blue-600 hover:bg-blue-100 hover:shadow-sm'
                                }`}
                                title="Editar agendamento"
                              >
                                <Edit size={18} />
                              </button>

                              <button
                                onClick={() => handleDelete(event)}
                                className={`p-2 rounded-full transition-all ${
                                  isDarkMode
                                    ? 'text-red-400 hover:bg-red-900/30 hover:shadow-inner'
                                    : 'text-red-600 hover:bg-red-100 hover:shadow-sm'
                                }`}
                                title="Excluir agendamento"
                              >
                                <Trash2 size={18} />
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className={`flex justify-end p-4 border-t ${isDarkMode ? 'border-neutral-700' : 'border-neutral-200'}`}>
          <button
            onClick={onClose}
            className={`px-4 py-2 rounded-md ${isDarkMode ? 'bg-neutral-700 text-white hover:bg-neutral-600' : 'bg-neutral-200 text-neutral-800 hover:bg-neutral-300'}`}
          >
            Fechar
          </button>
        </div>
      </div>

      {/* Diálogo de confirmação para exclusão */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => {
          setConfirmDialogOpen(false);
          setAppointmentToDelete(null);
        }}
        onConfirm={confirmDelete}
        title="Excluir Agendamento"
        message={appointmentToDelete ?
          `Tem certeza que deseja excluir o agendamento "${appointmentToDelete.title}"? Esta ação não pode ser desfeita.` :
          "Tem certeza que deseja excluir este agendamento?"}
        variant="danger"
        confirmText="Excluir"
        cancelText="Cancelar"
      />
    </div>
  );
};

export default MultipleEventsModal;
