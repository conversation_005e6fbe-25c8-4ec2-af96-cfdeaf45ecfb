-- CreateEnum
CREATE TYPE "SystemModule" AS ENUM ('ADMIN', 'RH', 'FINANCIAL', 'SCHEDULING', 'BASIC');

-- CreateEnum
CREATE TYPE "SchedulingStatus" AS ENUM ('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED', 'NO_SHOW');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "RecurrenceType" AS ENUM ('OCCURRENCES', 'END_DATE');

-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('SYSTEM_ADMIN', 'COMPANY_ADMIN', 'EMPLOYEE');

-- CreateEnum
CREATE TYPE "BillingCycle" AS ENUM ('MONTHLY', 'YEARLY');

-- CreateEnum
CREATE TYPE "SubscriptionStatus" AS ENUM ('ACTIVE', 'TRIAL', 'PAST_DUE', 'CANCELED', 'INCOMPLETE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "InvoiceStatus" AS ENUM ('PAID', 'PENDING', 'FAILED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ConversationType" AS ENUM ('INDIVIDUAL', 'GROUP');

-- CreateEnum
CREATE TYPE "MessageContentType" AS ENUM ('TEXT', 'IMAGE', 'FILE', 'LINK', 'SYSTEM');

-- CreateEnum
CREATE TYPE "MessageDeliveryStatus" AS ENUM ('SENT', 'DELIVERED', 'READ');

-- CreateEnum
CREATE TYPE "EvaluationType" AS ENUM ('SKILL_ACQUISITION', 'BEHAVIOR_REDUCTION');

-- CreateEnum
CREATE TYPE "ScoreType" AS ENUM ('ALWAYS', 'FREQUENTLY', 'SOMETIMES', 'RARELY', 'NEVER', 'NOT_APPLICABLE');

-- CreateEnum
CREATE TYPE "TeachingType" AS ENUM ('DISCRETE_TRIAL_STRUCTURED', 'TASK_ANALYSIS', 'NATURALISTIC_TEACHING', 'DISCRETE_TRIAL_INTERSPERSED');

-- CreateEnum
CREATE TYPE "CriteriaDegree" AS ENUM ('OMISSION', 'ERROR', 'MORE_INTRUSIVE', 'PARTIALLY_INTRUSIVE', 'LESS_INTRUSIVE', 'INDEPENDENT');

-- CreateEnum
CREATE TYPE "ProgramType" AS ENUM ('PROGRAM_CATALOG', 'LEARNING_PROGRAM');

-- CreateEnum
CREATE TYPE "ProgramStatus" AS ENUM ('unallocated', 'inTraining', 'completed');

-- CreateEnum
CREATE TYPE "SimNaoAsVezes" AS ENUM ('SIM', 'NAO', 'AS_VEZES');

-- CreateEnum
CREATE TYPE "StatusEvolucaoDiaria" AS ENUM ('RASCUNHO', 'FINALIZADA');

-- Baseline migration - Database already exists with current schema
-- This migration marks the current state as the baseline
SELECT 1;
