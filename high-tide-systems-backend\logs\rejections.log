{"timestamp": "2025-06-16 12:45:42", "level": "error", "message": "unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)", "error": {}, "stack": "Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)", "rejection": true, "date": "Mon Jun 16 2025 12:45:42 GMT+0000 (Coordinated Universal Time)", "process": {"pid": 120, "uid": 0, "gid": 0, "cwd": "/usr/src/app", "execPath": "/usr/local/bin/node", "version": "v18.20.0", "argv": ["/usr/local/bin/node", "/usr/src/app/src/server.js", "src/server.js"], "memoryUsage": {"rss": 158027776, "heapTotal": 54448128, "heapUsed": 51496744, "external": 3133863, "arrayBuffers": 270598}}, "os": {"loadavg": [1.47, 1.81, 1.7], "uptime": 3930.47}, "trace": [{"column": 31, "file": "/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js", "function": "Commander._RedisClient_sendCommand", "line": 520, "method": "_RedisClient_sendCommand", "native": false}, {"column": 154, "file": "/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js", "function": "Commander.<PERSON><PERSON>xe<PERSON>or", "line": 190, "method": "commandsExecutor", "native": false}, {"column": 29, "file": "/usr/src/app/node_modules/@redis/client/dist/lib/commander.js", "function": "BaseClass.<computed> [as publish]", "line": 8, "method": "<computed> [as publish]", "native": false}, {"column": 28, "file": "/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js", "function": "RedisAdapter.broadcast", "line": 473, "method": "broadcast", "native": false}, {"column": 26, "file": "/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js", "function": "BroadcastOperator.emit", "line": 169, "method": "emit", "native": false}, {"column": 44, "file": "/usr/src/app/src/socket/socketService.js", "function": "updateUserStatus", "line": 212, "method": null, "native": false}]}