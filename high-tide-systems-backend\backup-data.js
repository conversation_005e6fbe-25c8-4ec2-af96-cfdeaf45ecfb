const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function backupData() {
  try {
    console.log('📦 Iniciando backup dos dados...\n');

    const backup = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      data: {}
    };

    // 1. Backup das empresas
    console.log('🏢 Fazendo backup das empresas...');
    backup.data.companies = await prisma.company.findMany({
      include: {
        users: {
          select: {
            id: true,
            login: true,
            email: true,
            fullName: true,
            cpf: true,
            cnpj: true,
            birthDate: true,
            address: true,
            neighborhood: true,
            city: true,
            state: true,
            postalCode: true,
            phone: true,
            role: true,
            modules: true,
            permissions: true,
            active: true,
            createdAt: true,
            professionId: true,
            branchId: true
          }
        },
        subscription: {
          include: {
            modules: true,
            invoices: true
          }
        },
        branches: true
      }
    });
    console.log(`   ✅ ${backup.data.companies.length} empresas salvas`);

    // 2. Backup das profissões e grupos
    console.log('👨‍💼 Fazendo backup das profissões...');
    backup.data.professionGroups = await prisma.professionGroup.findMany({
      include: {
        professions: true
      }
    });
    console.log(`   ✅ ${backup.data.professionGroups.length} grupos de profissão salvos`);

    // 3. Backup dos agendamentos (se existirem)
    console.log('📅 Fazendo backup dos agendamentos...');
    backup.data.appointments = await prisma.appointment.findMany({
      include: {
        patient: true,
        professional: true,
        service: true,
        company: true
      }
    });
    console.log(`   ✅ ${backup.data.appointments.length} agendamentos salvos`);

    // 4. Backup dos pacientes (se existirem)
    console.log('🏥 Fazendo backup dos pacientes...');
    backup.data.patients = await prisma.patient.findMany({
      include: {
        company: true,
        appointments: true
      }
    });
    console.log(`   ✅ ${backup.data.patients.length} pacientes salvos`);

    // 5. Backup dos serviços (se existirem)
    console.log('🛠️ Fazendo backup dos serviços...');
    backup.data.services = await prisma.service.findMany({
      include: {
        company: true
      }
    });
    console.log(`   ✅ ${backup.data.services.length} serviços salvos`);

    // 6. Salvar backup em arquivo
    const backupDir = path.join(__dirname, 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const filename = `backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    const filepath = path.join(backupDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify(backup, null, 2));
    
    console.log(`\n✅ Backup concluído com sucesso!`);
    console.log(`📁 Arquivo salvo em: ${filepath}`);
    console.log(`📊 Resumo do backup:`);
    console.log(`   - ${backup.data.companies.length} empresas`);
    console.log(`   - ${backup.data.companies.reduce((acc, c) => acc + c.users.length, 0)} usuários`);
    console.log(`   - ${backup.data.professionGroups.length} grupos de profissão`);
    console.log(`   - ${backup.data.appointments.length} agendamentos`);
    console.log(`   - ${backup.data.patients.length} pacientes`);
    console.log(`   - ${backup.data.services.length} serviços`);

    return filepath;

  } catch (error) {
    console.error('❌ Erro ao fazer backup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  backupData();
}

module.exports = { backupData };
